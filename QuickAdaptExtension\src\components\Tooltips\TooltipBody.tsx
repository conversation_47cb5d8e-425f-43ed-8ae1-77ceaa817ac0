import React, { RefObject, useEffect, useRef, useState, useMemo } from "react";
import {
	Box,
	ClickAwayListener,
	IconButton,
	Paper,
	Popover,
	TextField,
	Tooltip,
	Typography,
	LinearProgress,
	MobileStepper,
	Breadcrumbs,
} from "@mui/material";
import { CustomWidthTooltip, EXTENSION_PART, TOOLTIP_HEIGHT, TOOLTIP_MN_WIDTH, TOOLTIP_MX_WIDTH } from "./Tooltip";
import AddIcon from "@mui/icons-material/Add";
import { Image, TextFormat, Code, VideoLibrary, GifBox, Link } from "@mui/icons-material";
import useDrawerStore, { TSectionType, TooltipState } from "../../store/drawerStore";
import ButtonSection from "./components/Buttons";
import ImageSection from "./components/ImageSection";
import RTEsection from "./components/RTE/RTESection";
import RTE from "./components/RTE/RTE";
import { createPortal } from "react-dom";
import CloseIcon from "@mui/icons-material/Close";
import JoditEditor from "jodit-react";
import AlertPopup from "../drawer/AlertPopup";
import PerfectScrollbar from 'react-perfect-scrollbar';
import "react-perfect-scrollbar/dist/css/styles.css";
import { useTranslation } from "react-i18next";

type SectionType = { type: "image" | "button" | "video" | "gif" | "html" } | { type: "text" };

// Maximum allowed sections of each type
const MAX_SECTIONS = {
	image: 3,
	button: 3,
	rte: 3,
};
const TooltipBody = ({
	isPopoverOpen,
	setIsPopoverOpen,
	popupPosition,
	isUnSavedChanges,
	openWarning,
	setopenWarning,
	handleLeave,
	updatedGuideData,
}: {
	isPopoverOpen: boolean;
	setIsPopoverOpen: (param: boolean) => void;
	popupPosition: any;
	isUnSavedChanges: boolean;
	openWarning: boolean;
	setopenWarning: (params: boolean) => void;
	handleLeave: () => void;
		updatedGuideData: any;
}) => {
	const { t: translate } = useTranslation();
	const {
		toolTipGuideMetaData,
		currentStep,
		handleTooltipRTEValue,
		handleRTEDeleteSection,
		handleRTECloneSection,
		tooltip,
		tooltipBackgroundcolor,
		openTooltip,
		selectedOption,
		selectedTemplate,
		selectedTemplateTour,
		setTooltipPositionByXpath,
		width,
		borderRadius,
		Annpadding,
		borderColor,
		tooltipborderradius,
		tooltipbordersize,
		tooltipBordercolor,
		tooltipPosition,
		tooltipWidth,
		tooltippadding,
		AnnborderSize,
		currentStepIndex,
		dismissData,
		steps,
		setCurrentHoveredElement,
		elementClick,
		dismiss,
		setDismiss,
		progress,
		setProgress,
		ProgressColor,
		setProgressColor
	} = useDrawerStore((state) => state);

	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const boxRef = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());

	const [savedRange, setSaveRange] = useState<Range | undefined>(undefined);
	const [currentIndex, setCurrentIndex] = useState<number>(0);
	const [currentRTEFocusedId, setCurrentRTEFocusedId] = useState<string>("");
	const [currentFocusedType, setCurrentFocusedType] = useState<TSectionType>();
	const [isEditorFocused, setIsEditorFocused] = useState(false);
	const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);

	// State to track if scrolling is needed
	const [needsScrolling, setNeedsScrolling] = useState(false);
	const contentRef = useRef<HTMLDivElement>(null);
	const scrollbarRef = useRef<any>(null);

	// Section count tracking for limits
	const [sectionCounts, setSectionCounts] = useState({
		image: 0,
		button: 0,
		rte: 0
	});

	// Helper function to check if a section type has reached its limit
	const hasReachedLimit = (type: TSectionType): boolean => {
		// Map the section type to the corresponding key in sectionCounts
		const countType = type === "rte" ? "rte" : type === "button" ? "button" : type === "image" ? "image" : null;

		// If the type is not supported, return false
		if (countType === null) return false;

		return sectionCounts[countType] >= MAX_SECTIONS[countType];
	};
	const handleAddIconClick = (event: React.MouseEvent<HTMLElement>, idx: number) => {
		if (
		hasReachedLimit("rte") &&
		hasReachedLimit("button") &&
		hasReachedLimit("image")
		) {
			return;
		}
		const currentTarget = event.currentTarget;
		setTimeout(() => {
			setCurrentIndex(idx);
			setAnchorEl(currentTarget);
		}, 0);
	};

	const handlePopoverClose = () => {
		setAnchorEl(null);
	};

	const handleFocus = (id: string) => {
		setIsPopoverOpen(true);
		setCurrentRTEFocusedId(id);
	};

	const handleBlur = (id: string) => {
		if (boxRef?.current.get(id)?.current?.innerHTML) {
			handleTooltipRTEValue(id, boxRef.current.get(id)?.current?.innerHTML.trim() || "");
		}
		// Hide toolbar when blurring
		setToolbarVisibleRTEId(null);
	};

	const handleDeleteSection = () => {
		handleRTEDeleteSection(currentRTEFocusedId);
	};
	const handleCloneContainer = () => {
		handleRTECloneSection(currentRTEFocusedId);
	};
	const [sections, setSections] = useState<SectionType[]>([{ type: "image" }, { type: "text" }, { type: "button" }]);
	const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
	const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
	const handleDragStart = (index: number) => {
		setDraggingIndex(index);
	};
	const handleDragEnter = (index: number) => {
		if (draggingIndex !== null && draggingIndex !== index) {
			const reorderedSections = [...sections];
			const [removed] = reorderedSections.splice(draggingIndex, 1);
			reorderedSections.splice(index, 0, removed);
			setSections(reorderedSections);
			setDraggingIndex(index);
		}
	};

	const handleDragEnd = () => {
		setDraggingIndex(null);
	};

	const addToBoxRef = (id: string) => {
		if (!boxRef.current.has(id)) {
			const newRef = React.createRef<HTMLDivElement>();
			boxRef.current.set(id, newRef);
			return newRef;
		}
		return boxRef.current.get(id);
	};

	// Update section counts when the component mounts or when containers change
	useEffect(() => {
		if (toolTipGuideMetaData && toolTipGuideMetaData[currentStep - 1]?.containers) {
			const containers = toolTipGuideMetaData[currentStep - 1].containers;
			const counts = {
				image: 0,
				button: 0,
				rte: 0
			};

			// Count each type of section
			containers.forEach(container => {
				if (container.type === "image") counts.image++;
				else if (container.type === "button") counts.button++;
				else if (container.type === "rte") counts.rte++;
			});

			setSectionCounts(counts);
		}
	}, [toolTipGuideMetaData, currentStep]);

	// Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};

		
		checkScrollNeeded();

		
		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];

		
		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}

		
		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [toolTipGuideMetaData, currentStep]);

	useEffect(() => {
		if (openTooltip) {
			if (typeof window !== "undefined") {
				const xpath = toolTipGuideMetaData[currentStep - 1]?.xpath?.value;

				// Check if XPath is valid and not empty
				if (xpath && xpath.trim() !== "") {
					const result = window.document.evaluate(
						xpath,
						window.document,
						null,
						XPathResult.FIRST_ORDERED_NODE_TYPE,
						null
					);

					const element = result.singleNodeValue as HTMLElement | null;
					if (element) {
						setCurrentHoveredElement(element);
						const rect = element.getBoundingClientRect();
						const info = {
							element,
							tagName: element.tagName,
							classes: element.className,
							id: element.id,
							position: {
								x: rect.x,
								y: rect.y,
								width: rect.width,
								height: rect.height,
							},
							textContent: element.textContent?.trim() || "",
						};
						setTooltipPositionByXpath(info);
					}
				}
			}
			setTimeout(() => {
				let popup = document.querySelector(".MuiTooltip-popperInteractive");
				if (popup instanceof HTMLElement && popupPosition) {
					//popup.style.top = `${popupPosition.top || "20px"}`;
					//popup.style.left = `${popupPosition.left || "10px"}`; // Example for setting the left position
				}
			}, 10);
		}
	}, [openTooltip]);

	const canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;

	return (
		<div>
		<div style={{ placeContent: "end", display: "flex" }}>
				{!isPopoverOpen && dismiss && (
					<IconButton className="qadpt-dismiss"
						
						aria-label="close"
					>
						<CloseIcon sx={{ zoom: "1", color: "#000" }} />
					</IconButton>
				)}
			</div>
			
			<PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "320px" }}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
		<div
			ref={contentRef}
			style={{
				// backgroundColor: canvasProperties?.backgroundColor || "white",
				// padding: canvasProperties?.padding || "4px",
				// width: `${toolTipGuideMetaData[currentStep - 1]?.canvas.width} !important` || "500px !important",
				// borderRadius: canvasProperties?.borderRadius || "8px",
				// minWidth: TOOLTIP_MN_WIDTH,
				// maxWidth: TOOLTIP_MX_WIDTH,
				minHeight: 60,
				// maxHeight: 320,
				// height: selectedTemplate === "Tooltip" ? "auto" : "350px",
				overflow: "hidden",
				position: "relative",
			}}
		>
			
			{toolTipGuideMetaData[currentStep - 1]?.containers.map((item, index) => {
				const id = `${item.type}|${item.id}`;
				const handleDragStart = (index: number) => {
					if (draggedIndex === index) return; // Prevent redundant drag starts
					setDraggedIndex(index);
				};

				const handleDragEnter = (index: number) => {
					if (draggedIndex !== null && draggedIndex !== index) {
						const updatedContainers = [...toolTipGuideMetaData[currentStep - 1].containers];
						const [draggedItem] = updatedContainers.splice(draggedIndex, 1);
						updatedContainers.splice(index, 0, draggedItem);

						// Update state only if the order has changed
						useDrawerStore.setState({
							toolTipGuideMetaData: [
								{
									...toolTipGuideMetaData[currentStep - 1],
									containers: updatedContainers,
								},
							],
						});

						setDraggedIndex(index);
					}
				};

				const handleDragEnd = () => {
					setDraggedIndex(null);
				};
				return (
					<>
						<div
							onMouseEnter={(e) => {
								setCurrentFocusedType(e.currentTarget.id as TSectionType);
							}}
							style={{
								position: "relative",
								padding: "7px 0",
								borderBottomWidth: currentFocusedType === id ? "1px" : "0px",
								borderBottomColor: currentFocusedType === id ? "var(--primarycolor)" : "transparent",
								borderBottomStyle: currentFocusedType === id ? "dotted" : "none",
								marginBottom:"10px"
							}}
							id={id}
							draggable={item.type !== "rte"}
							onDragStart={() => handleDragStart(index)}
							onDragEnter={() => handleDragEnter(index)}
							onDragEnd={handleDragEnd}
						>
							{item.type === "button" ? (
								<>
									<ButtonSection
										items={item}
										updatedGuideData={updatedGuideData}
										isCloneDisabled={hasReachedLimit("button")}
									/>
									{currentFocusedType === id && (
										<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />
									)}
								</>
							) : item.type === "image" ? (
								<>
									<ImageSection
										items={item}
										isCloneDisabled={hasReachedLimit("image")}
									/>
									{currentFocusedType === id && (
										<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />
									)}
								</>
							) : item.type === "rte" ? (
								<>
									<RTEsection
										items={item}
										//@ts-ignore
										boxRef={addToBoxRef(item.id)}
										handleFocus={handleFocus}
										handleeBlur={handleBlur}
										isPopoverOpen={isPopoverOpen}
										setIsPopoverOpen={setIsPopoverOpen}
										currentRTEFocusedId={currentRTEFocusedId}
										toolbarVisibleRTEId={toolbarVisibleRTEId}
										setToolbarVisibleRTEId={setToolbarVisibleRTEId}
										isCloneDisabled={hasReachedLimit("rte")}
									/>
									{currentFocusedType === id && (
										<AddSectionComp handleAddIconClick={(e) => handleAddIconClick(e, index)} />
									)}
								</>
							) : null}
						</div>
					</>
				);
			})}

			

			{/* {anchorEl ? ( */}
			<SectionPopOver
				anchorEl={anchorEl}
				handlePopoverClose={handlePopoverClose}
				currentIndex={currentIndex}
				hasReachedLimit={hasReachedLimit}
			/>
			{/* ) : null} */}
			{isUnSavedChanges && openWarning && (
				<AlertPopup
					openWarning={openWarning}
					setopenWarning={setopenWarning}
					handleLeave={handleLeave}
				/>
			)}
			{/* RTEToolbar is now integrated within the Jodit editor, no need for separate popup */}
				</div>
				</PerfectScrollbar>
		{progress && toolTipGuideMetaData.length>1 &&
				(selectedTemplate === "Tooltip"  || selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot") && 
				(selectedOption === 1 || selectedOption === "" ? (
					<DotsStepper
						activeStep={currentStep}
						steps={steps.length}
						ProgressColor = {ProgressColor}
					/>
				) : selectedOption === 2 ? (
					<div>
						<LinearProgress
							variant="determinate"
							//sx={{paddingTop:"15px",padding:"8px"}}
							value={(currentStep / steps.length) * 100}
							sx={{
								height: "6px",
								borderRadius: "20px",
								margin: "6px 10px",
								'& .MuiLinearProgress-bar': {
                                backgroundColor: ProgressColor, // progress bar color
                              },}}
						/>
					</div>
				) 
				: selectedOption === 3 ? (
					<div style={{padding:"8px"}}>
						<BreadCrumpStepper
						activeStep={currentStep}
						steps={steps.length}
						ProgressColor={ProgressColor}
						
					/>
					</div>
				) : selectedOption === 4 ? (
					<Breadcrumbs
						aria-label="breadcrumb"
						sx={{ padding: "8px" }}
					>
						<Typography sx={{color : ProgressColor}}>
							Step {currentStep} of {steps.length}
						</Typography>
					</Breadcrumbs>
				) : null)}
		</div>
	);
};
export default TooltipBody;

const AddSectionComp = ({
	handleAddIconClick,
}: {
	handleAddIconClick: (event: React.MouseEvent<HTMLElement>) => void;
}) => {
	return (
		<Box sx={{ position: "absolute", transform: "translate(-50%,-50%)", top: "99%", left: "50%" }}>
			<IconButton
				onClick={handleAddIconClick}
				sx={{
					backgroundColor: "#5F9EA0",
					"&:hover": {
						backgroundColor: "#70afaf",
					},
					borderRadius: "8px",
					padding: "5px !important",
				}}
			>
				<AddIcon
					fontSize="small"
					sx={{ color: "#fff" }}
				/>
			</IconButton>
		</Box>
	);
};

const SectionPopOver = ({
	anchorEl,
	handlePopoverClose,
	currentIndex,
	hasReachedLimit
}: {
	anchorEl: HTMLElement | null;
	handlePopoverClose: () => void;
	currentIndex: number;
	hasReachedLimit: (type: TSectionType) => boolean;
}) => {
	const { t: translate } = useTranslation();
	const { createTooltipSections, selectedOption, currentStep, steps, currentStepIndex } = useDrawerStore(
		(state: any) => state
	);
	const handleAddSection = (sectionType: TSectionType) => {
		// Don't add if limit is reached
		if (hasReachedLimit(sectionType)) {
			
			return;
		}

		createTooltipSections(sectionType, currentIndex);
		handlePopoverClose();
	};

	return (
		<>
			{anchorEl && (
				<Popover
					open={Boolean(anchorEl)}
					anchorEl={anchorEl}
					onClose={handlePopoverClose}
					anchorOrigin={{
						vertical: "bottom",
						horizontal: "center",
					}}
					transformOrigin={{
						vertical: "top",
						horizontal: "center",
					}}
					id="tooltip-section-popover"
					slotProps={{
						paper: {
							sx: {
							padding: "12px",
							display: "flex",
							gap: "16px",
							width: "auto",
						},
						},
						root: {
							// instead of writing sx on popover write here it also target to root and more clear
							sx: {
								zIndex: (theme) => theme.zIndex.tooltip + 1000,
							},
						},
					}}
					disableEnforceFocus={true}
				>
					<Box
						display="flex"
						flexDirection="row"
						gap="16px"
					>
						<Tooltip arrow
							title={hasReachedLimit("rte") ? translate("Maximum limit of 3 Rich Text sections reached", { defaultValue: "Maximum limit of 3 Rich Text sections reached" }) : ""}
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<Box
								display="flex"
								flexDirection="column"
								alignItems="center"
								sx={{
									cursor: hasReachedLimit("rte") ? "not-allowed" : "pointer",
									opacity: hasReachedLimit("rte") ? 0.5 : 1,
									svg: {
										fontSize: "24px !important"
									}
								}}
								onClick={() => !hasReachedLimit("rte") && handleAddSection("rte")}
							>
								<TextFormat />
								<Typography sx={{ fontSize: "11px !important" }}>{translate("Rich Text", { defaultValue: "Rich Text" })}</Typography>
							</Box>
						</Tooltip>

						<Tooltip arrow
							title={hasReachedLimit("button") ? translate("Maximum limit of 3 Button sections reached", { defaultValue: "Maximum limit of 3 Button sections reached" }) : ""}
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<Box
								display="flex"
								flexDirection="column"
								alignItems="center"
								sx={{
									cursor: hasReachedLimit("button") ? "not-allowed" : "pointer",
									opacity: hasReachedLimit("button") ? 0.5 : 1,
									svg: {
										fontSize: "24px !important"
									}
								}}
								onClick={() => !hasReachedLimit("button") && handleAddSection("button")}
							>
								<Link />
								<Typography sx={{ fontSize: "11px !important" }}>{translate("Button", { defaultValue: "Button" })}</Typography>
							</Box>
						</Tooltip>

						<Tooltip arrow
							title={hasReachedLimit("image") ? translate("Maximum limit of 3 Image sections reached", { defaultValue: "Maximum limit of 3 Image sections reached" }) : ""}
							PopperProps={{
								sx: {
									zIndex: 9999,
								},
							}}
						>
							<Box
								display="flex"
								flexDirection="column"
								alignItems="center"
								sx={{
									cursor: hasReachedLimit("image") ? "not-allowed" : "pointer",
									opacity: hasReachedLimit("image") ? 0.5 : 1,
									svg: {
										fontSize: "24px !important"
									}
								}}
								onClick={() => !hasReachedLimit("image") && handleAddSection("image")}
							>
								<Image />
								<Typography sx={{ fontSize: "11px !important" }}>{translate("Image", { defaultValue: "Image" })}</Typography>
							</Box>
						</Tooltip>

						{/* <Tooltip
					title="Coming Soon"
					PopperProps={{
						sx: {
							zIndex: 9999,
						},
					}}
				>
					<span>
						<Box
							display="flex"
							flexDirection="column"
							alignItems="center"
							sx={{ cursor: "pointer", opacity: 0.5 }}
							// onClick={() => handleAddSection("video")}
						>
							<VideoLibrary />
							<Typography variant="caption">Video</Typography>
						</Box>
					</span>
				</Tooltip>
				<Box
					display="flex"
					flexDirection="column"
					alignItems="center"
					sx={{ cursor: "pointer", opacity: 0.5 }}
					// onClick={() => handleAddSection("gif")}
				>
					<GifBox />
					<Typography variant="caption">Gif</Typography>
				</Box>
				<Tooltip
					title="Coming Soon"
					PopperProps={{
						sx: {
							zIndex: 9999,
						},
					}}
				>
					<span>
						<Box
							display="flex"
							flexDirection="column"
							alignItems="center"
							sx={{ cursor: "pointer", opacity: 0.5 }}
							// onClick={() => handleAddSection("html")}
						>
							<Code />
							<Typography variant="caption">HTML</Typography>
						</Box>
					</span>
				</Tooltip> */}
					</Box>
				</Popover>
			)}
		</>
	);
};

const RTEToolbar = ({
	isPopoverOpen,
	currentRTEFocusedId,
	tooltip,
	setIsPopoverOpen,
	boxRef,
	savedRange,
	handleDeleteSection,
	handleCloneContainer,
	setSaveRange,
	popupPosition,
	handleTooltipRTEValue,
	iseditorfocused,
	setIsEditorFocused,
	canvasproperties,
	isCloneDisabled,
}: {
	isPopoverOpen: boolean;
	currentRTEFocusedId: string;
	tooltip: TooltipState;
	setIsPopoverOpen: (params: boolean) => void;
	boxRef: React.RefObject<HTMLDivElement> | undefined;
	savedRange: Range | undefined;
	handleDeleteSection: () => void;
	handleCloneContainer: () => void;
	setSaveRange: (params: Range) => void;
	popupPosition: any;
	handleTooltipRTEValue: (id: string, value: string) => void;
	iseditorfocused: boolean;
	setIsEditorFocused: (params: boolean) => void;
	canvasproperties: any;
		isCloneDisabled?: boolean;
	// open: boolean;
}) => {
	// const handleRTEPopoverClose = (event: MouseEvent | TouchEvent) => {
	// 	const target = event.target as HTMLElement;

	// 	// Check if the target is within specific editable or toolbar elements
	// 	const isElement =
	// 		target.closest("[contenteditable]") ||
	// 		target.closest("#rte-toolbar-paper") ||
	// 		target.closest("#rte-alignment-menu") ||
	// 		target.closest("#rte-alignment-menu-items") ||
	// 		target.closest(target.id.startsWith("#rt-editor") ? `${target.id}` : "nope");
	// 	if (target && isElement) {
	// 		return;
	// 	}

	// 	// Check if the target is within Jodit Editor container
	// 	//const isJoditContainer = target.classList.contains("backdrop");
	// 	//const isClickedoutside =  target.classList.contains("quickAdopt-selection");

	// 	// Handle dynamic ID checks safely
	// 	// const isRTEditor = target.id.startsWith("rt-editor") && target.closest(`#${target.id}`);

	// 	// if (isJoditContainer || isElement || isRTEditor) {
	// 	// 	return;
	// 	// }

	// 	// Delay closing the popover slightly

	// 		setIsPopoverOpen(false);

	// };

	// const handleBlur = () => {
	// 	setIsEditorFocused(false);
	//     console.log("Editor lost focus");
	// };

	const handleFocus = () => {
		setIsEditorFocused(true);
		console.log("Editor is focused");
	};
	const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);
	useEffect(() => {
    const dir = document.body.getAttribute("dir") || "ltr";
    setIsRtlDirection(dir.toLowerCase() === "rtl");
}, []);
	const editorConfig = useMemo(
		() => ({
			direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,
            
			// Jodit uses 'direction' not just 'rtl'
					language:  'en', // Optional: change language as well
			toolbarSticky: false,
			toolbarAdaptive: false,
			autofocus: true, // Enable auto-focus for immediate interaction
			buttons: [
				"bold",
				"italic",
				"underline",
				"brush",
				"font",
				"fontsize",
				"link",
				{
					name: "more",
					iconURL: "https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg",
					list: [
						"source",
						"strikethrough",
						"ul",
						"ol",
						"image",
						"video",
						"table",
						"align",
						"undo",
						"redo",
						"|",
						"hr",
						"eraser",
						"copyformat",
						"symbol",
						"fullsize",
						"print",
						"superscript",
						"subscript",
						"|",
						"outdent",
						"indent",
						"paragraph",
					],
				},
			],
			events: {
				focus: handleFocus,
				//blur: handleBlur
				afterInit: (editor: any) => {
					// Ensure the editor is focused immediately after initialization
					setTimeout(() => {
						editor.focus();
					}, 0);
				},
			},
			maxHeight: "calc(100% - 90px)",
		}),
		[isRtlDirection]
	);

	return (
		<>
			{isPopoverOpen &&
				createPortal(
					// <ClickAwayListener onClickAway={(event) => {
					// 	const target = event.target as HTMLElement;
					// 	// const isJoditContainer = target.classList.contains("backdrop");
					// 	// const isClickedoutside = target.classList.contains("quickAdopt-selection");
					// 	const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
					// 	const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
					// 	const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(".jodit-dialog__panel")?.contains(event.target as Node);
					// 	const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
					// 	const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
					// 	// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container
					// 	const isInsideToolbarButton = (event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
					// 	// Check if the target is the specific "Insert" button (or similar button you want)
					// 	const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;
					// 	if (!isInsidePopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton) {
					// 		handleRTEPopoverClose(event);
					// 	}
					// }}>
					<Paper
						style={{
							position: "absolute",
							zIndex: 99999,
							left: (document.getElementById("Tooltip-unique")?.getBoundingClientRect()?.x ?? 150) + 3,
							// @ts-ignore
							top: document.getElementById("Tooltip-unique")?.getBoundingClientRect()?.y - 50 || 80,
							width: canvasproperties?.width || "300px",
						}}
						component={"div"}
						id="rte-toolbar-paper"
						role="presentation"
						sx={{
							"& .jodit-status-bar-link": {
								display: "none !important",
							},
							"& .jodit-ui-input__wrapper": {
								pointerEvents: "auto",
							},
							"& .jodit-toolbar-button button": {
								minWidth: "8px !important",
							},
							"& .jodit-workplace": {
								maxHeight: "120px !important",
								minHeight: "60px !important",
								height: "80px !important",
								overflowX: "auto !important",
							},
						}}
					>
						<JoditEditor
							value={boxRef?.current?.innerHTML || ""}
							className="qadpt-jodit"
							config={{
								...editorConfig,
								controls: {
									font: {
										list: {
											"Poppins, sans-serif": "Poppins",
											"Roboto, sans-serif": "Roboto",
											"Comic Sans MS, sans-serif": "Comic Sans MS",
											"Open Sans, sans-serif": "Open Sans",
											"Calibri, sans-serif": "Calibri",
											"Century Gothic, sans-serif": "Century Gothic",
										},
									},
								},
							}}
							onBlur={(newContent, event) => {
								const isInsidePopup = document.querySelector(".jodit-popup")?.contains(event.target as Node);
								const isInsideJoditPopup = document.querySelector(".jodit-wysiwyg")?.contains(event.target as Node);
								const isInsideWorkplacePopup = document
									.querySelector(".jodit-dialog__panel")
									?.contains(event.target as Node);
								const isSelectionMarker = (event.target as HTMLElement).id.startsWith("jodit-selection_marker_");
								const isLinkPopup = document.querySelector(".jodit-ui-input__input")?.contains(event.target as Node);
								// Check if the target is inside a button, and if it is, check if it's inside the toolbar button container
								const isInsideToolbarButton =
									(event.target as HTMLElement).closest(".jodit-toolbar-button__button") !== null;
								const isPasteEvent =
									event.type === "paste" ||
									(event.type === "keydown" &&
										(event as unknown as KeyboardEvent).ctrlKey &&
										(event as unknown as KeyboardEvent).key === "v");
								// Check if the target is the specific "Insert" button (or similar button you want)
								const isInsertButton = (event.target as HTMLElement).closest("button[aria-pressed='false']") !== null;
								const formElement = document.querySelector(".jodit-ui-form");

								if (
									boxRef?.current &&
									!isInsidePopup &&
									!isSelectionMarker &&
									!isLinkPopup &&
									!isInsideToolbarButton &&
									!isInsertButton &&
									!formElement &&
									isInsideWorkplacePopup === undefined
								) {
									boxRef.current.innerHTML = newContent;
									handleTooltipRTEValue(currentRTEFocusedId, newContent.trim());
									setIsPopoverOpen(false);
								}
							}}
							onChange={(newContent) => {
								if (boxRef?.current) {
									boxRef.current.innerHTML = newContent;
								}
							}}
						/>
					</Paper>,
					// </ClickAwayListener >
					document.body,
					"rte-toolbar-portal"
				)}
		</>
	);
};
const DotsStepper = ({ steps, activeStep, ProgressColor }: { steps: number; activeStep: number; ProgressColor:string }) => {
	return (
		<MobileStepper
			variant="dots"
			steps={steps}
			position="static"
			activeStep={activeStep - 1}
			sx={{ flexGrow: 1, display: "flex", justifyContent: "center" ,background:"inherit", "& .MuiMobileStepper-dotActive": {
				backgroundColor: ProgressColor, // active dot color
			  }}}
			nextButton={<></>}
			backButton={<></>}
		/>
	);
};
const BreadCrumpStepper = ({ steps, activeStep,ProgressColor }: { steps: number; activeStep: number; ProgressColor:any }) => {
	return (
		<Box sx={{
			display: 'flex',
			justifyContent: 'center',
			gap: "4px", // Adjust space between steps
			
		  }}>
		  {/* Custom Step Indicators */}
		
			{Array.from({ length: steps }).map((_, index) => (
			  <div
				key={index}
				style={{
				  width: '14px',
				  height: '4px',
				  backgroundColor: index === activeStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color
				  borderRadius: '100px',
				}}
			  />
			))}
		
		</Box>
	  );
};
