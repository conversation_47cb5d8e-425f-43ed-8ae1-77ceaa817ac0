[{"E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx": "1", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts": "2", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx": "3", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts": "4", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx": "5", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx": "6", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx": "7", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx": "8", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts": "9", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts": "10", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts": "11", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx": "12", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx": "13", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx": "14", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx": "15", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx": "16", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts": "17", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts": "18", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx": "19", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx": "20", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts": "21", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx": "22", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx": "23", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx": "24", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx": "25", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx": "26", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx": "27", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx": "28", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx": "29", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx": "30", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx": "31", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx": "32", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx": "33", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx": "34", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx": "35", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx": "36", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts": "37", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx": "38", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx": "39", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts": "40", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx": "41", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx": "42", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx": "43", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx": "44", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx": "45", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts": "46", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx": "47", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx": "48", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx": "49", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx": "50", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx": "51", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx": "52", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx": "53", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx": "54", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx": "55", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx": "56", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx": "57", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx": "58", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx": "59", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx": "60", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx": "61", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx": "62", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx": "63", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx": "64", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx": "65", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx": "66", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx": "67", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx": "68", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx": "69", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx": "70", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx": "71", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx": "72", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx": "73", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx": "74", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx": "75", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx": "76", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx": "77", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx": "78", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts": "79", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts": "80", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx": "81", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx": "82", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx": "83", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx": "84", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx": "85", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx": "86", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx": "87", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx": "88", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx": "89", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts": "90", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts": "91", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx": "92", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx": "93", "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts": "94"}, {"size": 604, "mtime": 1748929949904, "results": "95", "hashOfConfig": "96"}, {"size": 440, "mtime": 1748929949904, "results": "97", "hashOfConfig": "96"}, {"size": 2871, "mtime": 1753097390940, "results": "98", "hashOfConfig": "96"}, {"size": 3344, "mtime": 1753158986544, "results": "99", "hashOfConfig": "96"}, {"size": 244157, "mtime": 1753158986525, "results": "100", "hashOfConfig": "96"}, {"size": 6890, "mtime": 1753072098544, "results": "101", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1753072098560, "results": "102", "hashOfConfig": "96"}, {"size": 3112, "mtime": 1753072087888, "results": "103", "hashOfConfig": "96"}, {"size": 395581, "mtime": 1753097390956, "results": "104", "hashOfConfig": "96"}, {"size": 3927, "mtime": 1748930023076, "results": "105", "hashOfConfig": "96"}, {"size": 6144, "mtime": 1749531263982, "results": "106", "hashOfConfig": "96"}, {"size": 41336, "mtime": 1753158986533, "results": "107", "hashOfConfig": "96"}, {"size": 5077, "mtime": 1753095524074, "results": "108", "hashOfConfig": "96"}, {"size": 3731, "mtime": 1753072087856, "results": "109", "hashOfConfig": "96"}, {"size": 13824, "mtime": 1753097390956, "results": "110", "hashOfConfig": "96"}, {"size": 13085, "mtime": 1753095525402, "results": "111", "hashOfConfig": "96"}, {"size": 28388, "mtime": 1753072098575, "results": "112", "hashOfConfig": "96"}, {"size": 1898, "mtime": 1748930023076, "results": "113", "hashOfConfig": "96"}, {"size": 1954, "mtime": 1751432283612, "results": "114", "hashOfConfig": "96"}, {"size": 9087, "mtime": 1753158986542, "results": "115", "hashOfConfig": "96"}, {"size": 296570, "mtime": 1753072087794, "results": "116", "hashOfConfig": "96"}, {"size": 193, "mtime": 1748929949654, "results": "117", "hashOfConfig": "96"}, {"size": 9097, "mtime": 1753158986506, "results": "118", "hashOfConfig": "96"}, {"size": 30700, "mtime": 1753072098544, "results": "119", "hashOfConfig": "96"}, {"size": 3012, "mtime": 1753095523933, "results": "120", "hashOfConfig": "96"}, {"size": 2606, "mtime": 1753072098528, "results": "121", "hashOfConfig": "96"}, {"size": 33243, "mtime": 1753158986531, "results": "122", "hashOfConfig": "96"}, {"size": 23270, "mtime": 1753095523980, "results": "123", "hashOfConfig": "96"}, {"size": 13556, "mtime": 1753072087810, "results": "124", "hashOfConfig": "96"}, {"size": 26724, "mtime": 1753095523855, "results": "125", "hashOfConfig": "96"}, {"size": 49705, "mtime": 1751532053846, "results": "126", "hashOfConfig": "96"}, {"size": 7599, "mtime": 1753072087888, "results": "127", "hashOfConfig": "96"}, {"size": 30037, "mtime": 1753158986499, "results": "128", "hashOfConfig": "96"}, {"size": 11669, "mtime": 1753097390956, "results": "129", "hashOfConfig": "96"}, {"size": 24200, "mtime": 1751432283612, "results": "130", "hashOfConfig": "96"}, {"size": 4880, "mtime": 1750229130169, "results": "131", "hashOfConfig": "96"}, {"size": 9238, "mtime": 1748930023061, "results": "132", "hashOfConfig": "96"}, {"size": 1297, "mtime": 1748930023061, "results": "133", "hashOfConfig": "96"}, {"size": 1248, "mtime": 1748929949920, "results": "134", "hashOfConfig": "96"}, {"size": 14238, "mtime": 1748930023076, "results": "135", "hashOfConfig": "96"}, {"size": 2997, "mtime": 1753072087872, "results": "136", "hashOfConfig": "96"}, {"size": 3285, "mtime": 1753095524105, "results": "137", "hashOfConfig": "96"}, {"size": 2750, "mtime": 1753095523949, "results": "138", "hashOfConfig": "96"}, {"size": 955, "mtime": 1753072098528, "results": "139", "hashOfConfig": "96"}, {"size": 19907, "mtime": 1753072098560, "results": "140", "hashOfConfig": "96"}, {"size": 743, "mtime": 1748929949654, "results": "141", "hashOfConfig": "96"}, {"size": 25466, "mtime": 1753097390940, "results": "142", "hashOfConfig": "96"}, {"size": 2608, "mtime": 1748930023061, "results": "143", "hashOfConfig": "96"}, {"size": 34811, "mtime": 1753166949215, "results": "144", "hashOfConfig": "96"}, {"size": 7772, "mtime": 1753072087872, "results": "145", "hashOfConfig": "96"}, {"size": 16105, "mtime": 1753095524105, "results": "146", "hashOfConfig": "96"}, {"size": 29119, "mtime": 1753158986510, "results": "147", "hashOfConfig": "96"}, {"size": 6245, "mtime": 1748929949857, "results": "148", "hashOfConfig": "96"}, {"size": 2034, "mtime": 1753072098528, "results": "149", "hashOfConfig": "96"}, {"size": 29744, "mtime": 1753158986497, "results": "150", "hashOfConfig": "96"}, {"size": 1962, "mtime": 1748929949654, "results": "151", "hashOfConfig": "96"}, {"size": 27258, "mtime": 1753158986512, "results": "152", "hashOfConfig": "96"}, {"size": 2401, "mtime": 1753072087841, "results": "153", "hashOfConfig": "96"}, {"size": 702, "mtime": 1753072087841, "results": "154", "hashOfConfig": "96"}, {"size": 13889, "mtime": 1753072087841, "results": "155", "hashOfConfig": "96"}, {"size": 19040, "mtime": 1753072087856, "results": "156", "hashOfConfig": "96"}, {"size": 6625, "mtime": 1753072087872, "results": "157", "hashOfConfig": "96"}, {"size": 20321, "mtime": 1753072087872, "results": "158", "hashOfConfig": "96"}, {"size": 3236, "mtime": 1748929949779, "results": "159", "hashOfConfig": "96"}, {"size": 2848, "mtime": 1748929949811, "results": "160", "hashOfConfig": "96"}, {"size": 15285, "mtime": 1753072087825, "results": "161", "hashOfConfig": "96"}, {"size": 15261, "mtime": 1753158986514, "results": "162", "hashOfConfig": "96"}, {"size": 11208, "mtime": 1753072087856, "results": "163", "hashOfConfig": "96"}, {"size": 17207, "mtime": 1753158986529, "results": "164", "hashOfConfig": "96"}, {"size": 8476, "mtime": 1753072087856, "results": "165", "hashOfConfig": "96"}, {"size": 15571, "mtime": 1753072098560, "results": "166", "hashOfConfig": "96"}, {"size": 16126, "mtime": 1749557445376, "results": "167", "hashOfConfig": "96"}, {"size": 32334, "mtime": 1753166949207, "results": "168", "hashOfConfig": "96"}, {"size": 60407, "mtime": 1753072087810, "results": "169", "hashOfConfig": "96"}, {"size": 26698, "mtime": 1753158986504, "results": "170", "hashOfConfig": "96"}, {"size": 5258, "mtime": 1753072087872, "results": "171", "hashOfConfig": "96"}, {"size": 883, "mtime": 1748929949889, "results": "172", "hashOfConfig": "96"}, {"size": 3117, "mtime": 1753097390940, "results": "173", "hashOfConfig": "96"}, {"size": 7943, "mtime": 1748930023061, "results": "174", "hashOfConfig": "96"}, {"size": 491, "mtime": 1751432283612, "results": "175", "hashOfConfig": "96"}, {"size": 5504, "mtime": 1753072087841, "results": "176", "hashOfConfig": "96"}, {"size": 33137, "mtime": 1753095523918, "results": "177", "hashOfConfig": "96"}, {"size": 37236, "mtime": 1753095523902, "results": "178", "hashOfConfig": "96"}, {"size": 2931, "mtime": 1749010760558, "results": "179", "hashOfConfig": "96"}, {"size": 2669, "mtime": 1748929949748, "results": "180", "hashOfConfig": "96"}, {"size": 17277, "mtime": 1753095523871, "results": "181", "hashOfConfig": "96"}, {"size": 27631, "mtime": 1753095523871, "results": "182", "hashOfConfig": "96"}, {"size": 15520, "mtime": 1753168265214, "results": "183", "hashOfConfig": "96"}, {"size": 15436, "mtime": 1753158986501, "results": "184", "hashOfConfig": "96"}, {"size": 677, "mtime": 1753072098575, "results": "185", "hashOfConfig": "96"}, {"size": 6886, "mtime": 1753158986540, "results": "186", "hashOfConfig": "96"}, {"size": 7158, "mtime": 1753158986535, "results": "187", "hashOfConfig": "96"}, {"size": 9356, "mtime": 1753158986516, "results": "188", "hashOfConfig": "96"}, {"size": 1211, "mtime": 1753158986538, "results": "189", "hashOfConfig": "96"}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1c51j82", {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 226, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 61, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 54, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 57, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 31, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 28, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 19, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 51, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 49, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 62, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\index.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\reportWebVitals.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\App.tsx", ["472", "473", "474"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\UserInfoStore.ts", ["475"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\Drawer.tsx", ["476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579", "580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594", "595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\AuthProvider.tsx", ["702", "703", "704", "705", "706", "707"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\AccountContext.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\SnackbarContext.tsx", ["708", "709"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\drawerStore.ts", ["710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\historyStore.ts", ["730"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\store\\userSession.ts", ["731"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuidePopUp.tsx", ["732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759", "760", "761", "762", "763", "764", "765", "766", "767", "768", "769", "770", "771"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\GuideSettings.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\LogoutPopup.tsx", ["772"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\login\\ExtensionLogin.tsx", ["773", "774", "775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\GuideMenuOptions.tsx", ["789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\ScrapingService.ts", ["813", "814", "815", "816"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SaveGuideService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\GuideListServices.tsx", ["817", "818", "819"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\assets\\icons\\icons.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\UseAuth.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistLauncherPreview.tsx", ["820", "821", "822", "823", "824", "825", "826", "827"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPopup.tsx", ["828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\UndoRedoButtons.tsx", ["855", "856", "857", "858"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\TrainingField.tsx", ["859", "860", "861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Design.tsx", ["875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930", "931", "932", "933", "934", "935"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\Banners.tsx", ["936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Bannerspreview\\Banner.tsx", ["958", "959", "960", "961", "962", "963", "964", "965", "966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\AnnouncementPreview.tsx", ["979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\HotspotPreview.tsx", ["1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourTemplate.tsx", ["1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\Tooltip.tsx", ["1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051", "1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087", "1088", "1089"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\stepPopup.tsx", ["1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\tourPreview.tsx", ["1099", "1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112", "1113", "1114", "1115", "1116", "1117", "1118", "1119"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltipuserview.tsx", ["1120", "1121", "1122", "1123", "1124", "1125"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\OrganizationService.ts", ["1126", "1127", "1128", "1129"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\FileService.tsx", ["1130", "1131"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\LoginService.tsx", ["1132", "1133", "1134"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\utils\\historyUtils.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementRules.tsx", ["1135", "1136"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PageTrigger.tsx", ["1137"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\drawer\\AlertPopup.tsx", ["1138", "1139", "1140"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AI\\StopScrapingButton.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\PopupList.tsx", ["1141", "1142", "1143", "1144", "1145", "1146", "1147", "1148", "1149", "1150", "1151", "1152"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\auth\\OidcConfig.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Imagesection.tsx", ["1153", "1154", "1155", "1156", "1157"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\APIService.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\RTEsection.tsx", ["1158", "1159", "1160"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\VideoSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\Button.tsx", ["1161", "1162", "1163", "1164", "1165", "1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistPreview.tsx", ["1188", "1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\PopupSections\\HtmlSection.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\CreateWithAIButton.tsx", ["1199", "1200", "1201", "1202", "1203"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\AIAgent\\ModernChatWindow.tsx", ["1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCheckIcon.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\LauncherSettings.tsx", ["1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239", "1240", "1241", "1242", "1243", "1244", "1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267", "1268", "1269", "1270", "1271"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ImageCarousel.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\VideoPlayer.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\Chekpoints.tsx", ["1272", "1273", "1274", "1275", "1276", "1277", "1278", "1279", "1280", "1281", "1282", "1283", "1284", "1285", "1286", "1287", "1288", "1289", "1290", "1291", "1292", "1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1326", "1327", "1328"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CanvasSettings.tsx", ["1329", "1330", "1331", "1332"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Overlay.tsx", ["1333", "1334", "1335", "1336", "1337", "1338"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\ElementsSettings.tsx", ["1339", "1340", "1341", "1342", "1343", "1344", "1345", "1346", "1347", "1348", "1349", "1350", "1351", "1352", "1353", "1354", "1355", "1356", "1357", "1358"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\Animation.tsx", ["1359", "1360", "1361"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideDesign\\CustomCss.tsx", ["1362", "1363"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\ChecklistCanvasSettings.tsx", ["1364", "1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379", "1380", "1381", "1382", "1383", "1384", "1385", "1386", "1387", "1388", "1389", "1390", "1391", "1392", "1393", "1394", "1395", "1396"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\TitleSubTitle.tsx", ["1397", "1398", "1399", "1400", "1401", "1402", "1403", "1404", "1405", "1406", "1407", "1408", "1409", "1410", "1411", "1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424", "1425", "1426", "1427"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\PageInteraction.tsx", ["1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ButtonSettings.tsx", ["1456", "1457", "1458", "1459", "1460", "1461", "1462", "1463", "1464", "1465", "1466", "1467", "1468", "1469", "1470", "1471", "1472", "1473", "1474", "1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482", "1483", "1484", "1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageSectionField.tsx", ["1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\hotspot\\HotspotSettings.tsx", ["1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\tours\\BannerStepPreview.tsx", ["1546", "1547", "1548", "1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556", "1557", "1558", "1559", "1560", "1561", "1562", "1563", "1564", "1565"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\TooltipBody.tsx", ["1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601", "1602", "1603", "1604", "1605", "1606", "1607", "1608", "1609", "1610", "1611", "1612", "1613", "1614", "1615", "1616"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\GuidesPreview\\tooltippreview\\Tooltips\\Tooltips.tsx", ["1617", "1618", "1619", "1620", "1621", "1622", "1623", "1624", "1625", "1626", "1627", "1628", "1629", "1630", "1631", "1632"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\designFields\\TooltipCanvasSettings.tsx", ["1633", "1634", "1635", "1636", "1637", "1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645", "1646", "1647", "1648", "1649"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\CloneGuidePopUp.tsx", ["1650", "1651"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideSetting\\guideList\\TimeZoneConversion.tsx", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\SelectImageFromApplication.tsx", ["1652", "1653", "1654", "1655", "1656", "1657", "1658", "1659"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\SpeechRecognitionService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\AIService.ts", [], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\DraggableCheckpoint.tsx", ["1660", "1661", "1662", "1663", "1664", "1665", "1666", "1667", "1668", "1669", "1670", "1671", "1672", "1673", "1674", "1675", "1676", "1677", "1678", "1679", "1680", "1681", "1682", "1683", "1684", "1685", "1686", "1687", "1688"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointEditPopup.tsx", ["1689", "1690", "1691", "1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702", "1703", "1704", "1705", "1706", "1707", "1708", "1709", "1710", "1711", "1712", "1713", "1714", "1715", "1716", "1717", "1718", "1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727", "1728", "1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\checklist\\CheckpointAddPopup.tsx", ["1738", "1739", "1740", "1741", "1742", "1743", "1744", "1745", "1746", "1747", "1748", "1749", "1750", "1751", "1752", "1753", "1754", "1755", "1756", "1757", "1758", "1759", "1760", "1761", "1762", "1763", "1764", "1765", "1766", "1767", "1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776", "1777", "1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786", "1787", "1788", "1789", "1790", "1791", "1792", "1793", "1794", "1795", "1796", "1797", "1798", "1799"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageProperties.tsx", ["1800", "1801", "1802", "1803", "1804", "1805", "1806", "1807", "1808", "1809", "1810", "1811", "1812", "1813"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\guideBanners\\selectedpopupfields\\ImageGalleryPopup.tsx", ["1814", "1815"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\Buttons.tsx", ["1816", "1817", "1818", "1819", "1820", "1821", "1822", "1823", "1824", "1825", "1826"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ImageSection.tsx", ["1827", "1828", "1829", "1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\RTE\\RTESection.tsx", ["1840", "1841", "1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855", "1856", "1857", "1858", "1859", "1860", "1861", "1862", "1863", "1864"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\Tooltips\\components\\ButtonSetting.tsx", ["1865", "1866", "1867", "1868", "1869", "1870", "1871", "1872", "1873", "1874", "1875", "1876", "1877"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\services\\UserRoleService.ts", ["1878"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\i18n.ts", ["1879", "1880", "1881"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\contexts\\TranslationContext.tsx", ["1882", "1883"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\components\\common\\LanguageSelector.tsx", ["1884"], [], "E:\\quixy\\newadopt\\quickadapt\\QuickAdaptExtension\\src\\multilinguial\\LanguageService.ts", [], [], {"ruleId": "1885", "severity": 1, "message": "1886", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1889", "line": 9, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "1890", "line": 16, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1891", "line": 1, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "1892", "line": 1, "column": 58, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 65}, {"ruleId": "1885", "severity": 1, "message": "1893", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "1894", "line": 6, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1895", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "1896", "line": 12, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 40}, {"ruleId": "1885", "severity": 1, "message": "1897", "line": 17, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "1898", "line": 22, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1899", "line": 23, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1900", "line": 24, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1901", "line": 25, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1902", "line": 26, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1903", "line": 27, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "1904", "line": 28, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1905", "line": 29, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "1906", "line": 30, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 30, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1907", "line": 31, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1908", "line": 32, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 32, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1909", "line": 33, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 33, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "1910", "line": 34, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1911", "line": 35, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 35, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1912", "line": 37, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 37, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1913", "line": 38, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1914", "line": 39, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1915", "line": 40, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 40, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 44, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1917", "line": 50, "column": 20, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 60, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "1919", "line": 61, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "1920", "line": 68, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 68, "endColumn": 6}, {"ruleId": "1885", "severity": 1, "message": "1921", "line": 69, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 69, "endColumn": 6}, {"ruleId": "1885", "severity": 1, "message": "1922", "line": 76, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 76, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "1923", "line": 78, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 78, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1924", "line": 79, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 79, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "1925", "line": 79, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 79, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "1926", "line": 82, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "1927", "line": 83, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 83, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1928", "line": 84, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 84, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1929", "line": 88, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 88, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "1930", "line": 90, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 90, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "1931", "line": 96, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 96, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1932", "line": 103, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1933", "line": 106, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 106, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1934", "line": 107, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "1935", "line": 112, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 112, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1936", "line": 112, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 112, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "1937", "line": 115, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 115, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1938", "line": 122, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 122, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1939", "line": 136, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 136, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1940", "line": 199, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 199, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1941", "line": 216, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 216, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1942", "line": 224, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 224, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1943", "line": 380, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 380, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1944", "line": 415, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 415, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "1945", "line": 417, "column": 6, "nodeType": "1887", "messageId": "1888", "endLine": 417, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "1946", "line": 419, "column": 6, "nodeType": "1887", "messageId": "1888", "endLine": 419, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "1947", "line": 435, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 435, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1948", "line": 436, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 436, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "1949", "line": 438, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 438, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "1950", "line": 441, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 441, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "1951", "line": 445, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 445, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "1952", "line": 446, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 446, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "1953", "line": 457, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 457, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1954", "line": 458, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 458, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1955", "line": 459, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 459, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1956", "line": 461, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 461, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1957", "line": 461, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 461, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "1958", "line": 466, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 466, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "1959", "line": 466, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 466, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "1960", "line": 468, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 468, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1961", "line": 468, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 468, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "1962", "line": 471, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 471, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1963", "line": 471, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 471, "endColumn": 40}, {"ruleId": "1885", "severity": 1, "message": "1964", "line": 472, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 472, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "1965", "line": 477, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 477, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "1966", "line": 477, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 477, "endColumn": 50}, {"ruleId": "1885", "severity": 1, "message": "1967", "line": 484, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 484, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1968", "line": 484, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 484, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1969", "line": 486, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 486, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "1970", "line": 486, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 486, "endColumn": 41}, {"ruleId": "1885", "severity": 1, "message": "1971", "line": 488, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 488, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1972", "line": 488, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 488, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1973", "line": 501, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 501, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "1974", "line": 502, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 502, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "1975", "line": 502, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 502, "endColumn": 58}, {"ruleId": "1885", "severity": 1, "message": "1976", "line": 505, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 505, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "1977", "line": 505, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 505, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "1978", "line": 506, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 506, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "1979", "line": 506, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 506, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "1980", "line": 507, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 507, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1981", "line": 507, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 507, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "1982", "line": 516, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 516, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "1983", "line": 517, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 517, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1984", "line": 523, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 523, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "1985", "line": 527, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 527, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1986", "line": 527, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 527, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "1987", "line": 530, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 530, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1988", "line": 530, "column": 20, "nodeType": "1887", "messageId": "1888", "endLine": 530, "endColumn": 32}, {"ruleId": "1989", "severity": 1, "message": "1990", "line": 570, "column": 5, "nodeType": "1991", "endLine": 570, "endColumn": 27, "suggestions": "1992"}, {"ruleId": "1885", "severity": 1, "message": "1993", "line": 580, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 580, "endColumn": 6}, {"ruleId": "1885", "severity": 1, "message": "1994", "line": 581, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 581, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 582, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 582, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1996", "line": 584, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 584, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "1997", "line": 585, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 585, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1998", "line": 590, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 590, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1999", "line": 591, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 591, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2000", "line": 626, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 626, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2001", "line": 627, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 627, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2002", "line": 628, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 628, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2003", "line": 636, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 636, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 638, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 638, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2005", "line": 639, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 639, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2006", "line": 640, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 640, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2007", "line": 641, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 641, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2008", "line": 646, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 646, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2009", "line": 648, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 648, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2010", "line": 650, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 650, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2011", "line": 660, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 660, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2012", "line": 661, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 661, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2013", "line": 664, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 664, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2014", "line": 668, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 668, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2015", "line": 670, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 670, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2016", "line": 671, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 671, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2017", "line": 673, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 673, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2018", "line": 680, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 680, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2019", "line": 681, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 681, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2020", "line": 686, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 686, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2021", "line": 687, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 687, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2022", "line": 688, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 688, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2023", "line": 698, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 698, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2024", "line": 702, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 702, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2025", "line": 706, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 706, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2026", "line": 708, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 708, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2027", "line": 710, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 710, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2028", "line": 711, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 711, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2029", "line": 716, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 716, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2030", "line": 717, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 717, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2031", "line": 728, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 728, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2032", "line": 735, "column": 18, "nodeType": "1887", "messageId": "1888", "endLine": 735, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2033", "line": 736, "column": 18, "nodeType": "1887", "messageId": "1888", "endLine": 736, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2034", "line": 740, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 740, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2035", "line": 752, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 752, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2036", "line": 777, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 777, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2037", "line": 788, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 788, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2038", "line": 793, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 793, "endColumn": 42}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 798, "column": 22, "nodeType": "2041", "messageId": "2042", "endLine": 798, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2043", "line": 884, "column": 5, "nodeType": "1991", "endLine": 884, "endColumn": 46, "suggestions": "2044"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 884, "column": 6, "nodeType": "2046", "endLine": 884, "endColumn": 29}, {"ruleId": "1989", "severity": 1, "message": "2047", "line": 902, "column": 5, "nodeType": "1991", "endLine": 902, "endColumn": 18, "suggestions": "2048"}, {"ruleId": "1885", "severity": 1, "message": "2049", "line": 904, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 904, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2050", "line": 905, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 905, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2051", "line": 926, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 926, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2052", "line": 950, "column": 8, "nodeType": "2053", "endLine": 952, "endColumn": 3}, {"ruleId": "1989", "severity": 1, "message": "2054", "line": 987, "column": 5, "nodeType": "1991", "endLine": 995, "endColumn": 3, "suggestions": "2055"}, {"ruleId": "1989", "severity": 1, "message": "2056", "line": 1023, "column": 5, "nodeType": "1991", "endLine": 1046, "endColumn": 3, "suggestions": "2057"}, {"ruleId": "1989", "severity": 1, "message": "2058", "line": 1164, "column": 5, "nodeType": "1991", "endLine": 1164, "endColumn": 39, "suggestions": "2059"}, {"ruleId": "1885", "severity": 1, "message": "2060", "line": 1281, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 1281, "endColumn": 24}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1373, "column": 25, "nodeType": "2041", "messageId": "2042", "endLine": 1373, "endColumn": 27}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1380, "column": 25, "nodeType": "2041", "messageId": "2042", "endLine": 1380, "endColumn": 27}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1380, "column": 53, "nodeType": "2041", "messageId": "2042", "endLine": 1380, "endColumn": 55}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1383, "column": 26, "nodeType": "2041", "messageId": "2042", "endLine": 1383, "endColumn": 28}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1383, "column": 58, "nodeType": "2041", "messageId": "2042", "endLine": 1383, "endColumn": 60}, {"ruleId": "1885", "severity": 1, "message": "2062", "line": 1514, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1514, "endColumn": 33}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 1591, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 1591, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2063", "line": 1738, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1738, "endColumn": 30}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 2000, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 2000, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 2172, "column": 25, "nodeType": "2041", "messageId": "2042", "endLine": 2172, "endColumn": 27}, {"ruleId": "1989", "severity": 1, "message": "2064", "line": 2204, "column": 5, "nodeType": "1991", "endLine": 2204, "endColumn": 18, "suggestions": "2065"}, {"ruleId": "1885", "severity": 1, "message": "2066", "line": 2261, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 2261, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "2067", "line": 2268, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 2268, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2068", "line": 2271, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 2271, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2069", "line": 2271, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 2271, "endColumn": 48}, {"ruleId": "1885", "severity": 1, "message": "2070", "line": 2663, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 2663, "endColumn": 27}, {"ruleId": "1989", "severity": 1, "message": "2071", "line": 2698, "column": 5, "nodeType": "1991", "endLine": 2698, "endColumn": 38, "suggestions": "2072"}, {"ruleId": "1885", "severity": 1, "message": "2073", "line": 2715, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 2715, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2074", "line": 2749, "column": 6, "nodeType": "1887", "messageId": "1888", "endLine": 2749, "endColumn": 18}, {"ruleId": "1989", "severity": 1, "message": "2075", "line": 3149, "column": 4, "nodeType": "1991", "endLine": 3149, "endColumn": 18, "suggestions": "2076"}, {"ruleId": "1885", "severity": 1, "message": "2077", "line": 3493, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3493, "endColumn": 33}, {"ruleId": "1989", "severity": 1, "message": "2078", "line": 3567, "column": 16, "nodeType": "2046", "endLine": 3567, "endColumn": 37}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3568, "column": 56, "nodeType": "2041", "messageId": "2042", "endLine": 3568, "endColumn": 58}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3572, "column": 49, "nodeType": "2041", "messageId": "2042", "endLine": 3572, "endColumn": 51}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3576, "column": 50, "nodeType": "2041", "messageId": "2042", "endLine": 3576, "endColumn": 52}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3582, "column": 51, "nodeType": "2041", "messageId": "2042", "endLine": 3582, "endColumn": 53}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3589, "column": 51, "nodeType": "2041", "messageId": "2042", "endLine": 3589, "endColumn": 53}, {"ruleId": "1885", "severity": 1, "message": "2079", "line": 3812, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3812, "endColumn": 23}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 3820, "column": 30, "nodeType": "2041", "messageId": "2042", "endLine": 3820, "endColumn": 32}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3833, "column": 39, "nodeType": "2041", "messageId": "2042", "endLine": 3833, "endColumn": 41}, {"ruleId": "1989", "severity": 1, "message": "2080", "line": 3847, "column": 5, "nodeType": "1991", "endLine": 3847, "endColumn": 33, "suggestions": "2081"}, {"ruleId": "1885", "severity": 1, "message": "2082", "line": 3851, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 3851, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2083", "line": 3851, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 3851, "endColumn": 52}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 3949, "column": 55, "nodeType": "2041", "messageId": "2042", "endLine": 3949, "endColumn": 57}, {"ruleId": "1885", "severity": 1, "message": "2084", "line": 3968, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3968, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2085", "line": 3970, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 3970, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2086", "line": 3974, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3974, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2087", "line": 3993, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 3993, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 4019, "column": 66, "nodeType": "2041", "messageId": "2042", "endLine": 4019, "endColumn": 68}, {"ruleId": "1989", "severity": 1, "message": "2088", "line": 4026, "column": 5, "nodeType": "1991", "endLine": 4033, "endColumn": 3, "suggestions": "2089"}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 4267, "column": 17, "nodeType": "2041", "messageId": "2042", "endLine": 4267, "endColumn": 19}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 4522, "column": 21, "nodeType": "2041", "messageId": "2042", "endLine": 4522, "endColumn": 23}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 4530, "column": 21, "nodeType": "2041", "messageId": "2042", "endLine": 4530, "endColumn": 23}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 4543, "column": 15, "nodeType": "2041", "messageId": "2042", "endLine": 4543, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2090", "line": 4840, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 4840, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2091", "line": 4851, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 4851, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2092", "line": 4852, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 4852, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2093", "line": 4858, "column": 5, "nodeType": "1991", "endLine": 4858, "endColumn": 62, "suggestions": "2094"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 4858, "column": 6, "nodeType": "2095", "endLine": 4858, "endColumn": 48}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 4881, "column": 25, "nodeType": "2041", "messageId": "2042", "endLine": 4881, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2096", "line": 4885, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4885, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2097", "line": 4908, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 4908, "endColumn": 23}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 4983, "column": 25, "nodeType": "2041", "messageId": "2042", "endLine": 4983, "endColumn": 27}, {"ruleId": "1989", "severity": 1, "message": "2098", "line": 5016, "column": 5, "nodeType": "1991", "endLine": 5016, "endColumn": 22, "suggestions": "2099"}, {"ruleId": "1885", "severity": 1, "message": "2100", "line": 5018, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5018, "endColumn": 18}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 5020, "column": 40, "nodeType": "2041", "messageId": "2042", "endLine": 5020, "endColumn": 42}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 5085, "column": 69, "nodeType": "2041", "messageId": "2042", "endLine": 5085, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2101", "line": 5135, "column": 12, "nodeType": "1887", "messageId": "1888", "endLine": 5135, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2102", "line": 5136, "column": 12, "nodeType": "1887", "messageId": "1888", "endLine": 5136, "endColumn": 22}, {"ruleId": "1989", "severity": 1, "message": "2103", "line": 5166, "column": 5, "nodeType": "1991", "endLine": 5166, "endColumn": 38, "suggestions": "2104"}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 5169, "column": 40, "nodeType": "2041", "messageId": "2042", "endLine": 5169, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2101", "line": 5175, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5175, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2102", "line": 5176, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5176, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2105", "line": 5182, "column": 5, "nodeType": "1991", "endLine": 5182, "endColumn": 106, "suggestions": "2106"}, {"ruleId": "1989", "severity": 1, "message": "2107", "line": 5295, "column": 5, "nodeType": "1991", "endLine": 5295, "endColumn": 17, "suggestions": "2108"}, {"ruleId": "1989", "severity": 1, "message": "2109", "line": 5311, "column": 5, "nodeType": "1991", "endLine": 5311, "endColumn": 78, "suggestions": "2110"}, {"ruleId": "1885", "severity": 1, "message": "2111", "line": 5314, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5314, "endColumn": 29}, {"ruleId": "1989", "severity": 1, "message": "2112", "line": 5327, "column": 8, "nodeType": "1991", "endLine": 5327, "endColumn": 15, "suggestions": "2113"}, {"ruleId": "2114", "severity": 1, "message": "2115", "line": 5924, "column": 80, "nodeType": "2116", "messageId": "2117", "endLine": 5924, "endColumn": 81, "suggestions": "2118"}, {"ruleId": "1885", "severity": 1, "message": "2119", "line": 6129, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 6129, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2120", "line": 2, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2121", "line": 7, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2122", "line": 7, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2123", "line": 98, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 98, "endColumn": 25}, {"ruleId": "1989", "severity": 1, "message": "2124", "line": 103, "column": 6, "nodeType": "1991", "endLine": 103, "endColumn": 8, "suggestions": "2125"}, {"ruleId": "1885", "severity": 1, "message": "2126", "line": 148, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 148, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2127", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2128", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2129", "line": 3, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2130", "line": 8, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2131", "line": 9, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2132", "line": 13, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 46}, {"ruleId": "2133", "severity": 1, "message": "2134", "line": 2414, "column": 5, "nodeType": "2135", "messageId": "2042", "endLine": 2414, "endColumn": 17}, {"ruleId": "2133", "severity": 1, "message": "2136", "line": 2415, "column": 5, "nodeType": "2135", "messageId": "2042", "endLine": 2415, "endColumn": 20}, {"ruleId": "2133", "severity": 1, "message": "2137", "line": 2746, "column": 5, "nodeType": "2135", "messageId": "2042", "endLine": 2746, "endColumn": 24}, {"ruleId": "2133", "severity": 1, "message": "2138", "line": 2923, "column": 5, "nodeType": "2135", "messageId": "2042", "endLine": 2923, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2139", "line": 3627, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 3627, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2139", "line": 3826, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 3826, "endColumn": 28}, {"ruleId": "2133", "severity": 1, "message": "2140", "line": 5313, "column": 5, "nodeType": "2135", "messageId": "2042", "endLine": 5313, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2141", "line": 5385, "column": 14, "nodeType": "1887", "messageId": "1888", "endLine": 5385, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2142", "line": 6476, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 6476, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2142", "line": 6502, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 6502, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2142", "line": 6508, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 6508, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2142", "line": 6523, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 6523, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2141", "line": 7300, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 7300, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2141", "line": 7544, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 7544, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2141", "line": 7715, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 7715, "endColumn": 16}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 8380, "column": 66, "nodeType": "2041", "messageId": "2042", "endLine": 8380, "endColumn": 68}, {"ruleId": "1885", "severity": 1, "message": "2143", "line": 70, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 70, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2144", "line": 1, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2145", "line": 2, "column": 44, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 56}, {"ruleId": "1885", "severity": 1, "message": "2146", "line": 18, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2147", "line": 19, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 20, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2149", "line": 21, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 21, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2150", "line": 24, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2151", "line": 25, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2152", "line": 26, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2153", "line": 31, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2154", "line": 38, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 55}, {"ruleId": "1885", "severity": 1, "message": "2155", "line": 38, "column": 63, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 70}, {"ruleId": "1885", "severity": 1, "message": "2156", "line": 46, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2157", "line": 48, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2158", "line": 92, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2159", "line": 93, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 93, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2160", "line": 99, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2161", "line": 100, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2162", "line": 104, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2163", "line": 108, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 108, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2164", "line": 112, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 112, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2165", "line": 113, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 113, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2166", "line": 114, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 114, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2167", "line": 115, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 115, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2168", "line": 116, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 116, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 119, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 119, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2170", "line": 120, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 120, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2171", "line": 162, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 162, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2172", "line": 172, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 172, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2173", "line": 180, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 180, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2174", "line": 181, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 181, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2175", "line": 182, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 182, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2176", "line": 183, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 183, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2177", "line": 184, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 184, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2178", "line": 205, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 205, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2179", "line": 209, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 209, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2180", "line": 455, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 455, "endColumn": 26}, {"ruleId": "1989", "severity": 1, "message": "2181", "line": 547, "column": 5, "nodeType": "1991", "endLine": 547, "endColumn": 60, "suggestions": "2182"}, {"ruleId": "1885", "severity": 1, "message": "2183", "line": 561, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 561, "endColumn": 22}, {"ruleId": "1989", "severity": 1, "message": "2184", "line": 581, "column": 5, "nodeType": "1991", "endLine": 581, "endColumn": 60, "suggestions": "2185"}, {"ruleId": "1989", "severity": 1, "message": "2186", "line": 598, "column": 4, "nodeType": "1991", "endLine": 598, "endColumn": 6, "suggestions": "2187"}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2189", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "1926", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "1927", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "1924", "line": 5, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "1925", "line": 5, "column": 46, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 55}, {"ruleId": "1885", "severity": 1, "message": "2191", "line": 6, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2192", "line": 6, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "1936", "line": 11, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2193", "line": 17, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2194", "line": 21, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 21, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2195", "line": 24, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2196", "line": 25, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2197", "line": 26, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1969", "line": 35, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 35, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "1970", "line": 35, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 35, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1901", "line": 6, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2198", "line": 9, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2199", "line": 12, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2200", "line": 25, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2201", "line": 51, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2202", "line": 52, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2008", "line": 53, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2203", "line": 54, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2204", "line": 55, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 55, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2025", "line": 56, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2205", "line": 57, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 57, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2206", "line": 58, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2026", "line": 59, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2207", "line": 60, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2208", "line": 61, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2209", "line": 62, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 62, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2210", "line": 63, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 63, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2211", "line": 64, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 64, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2165", "line": 65, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 65, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2168", "line": 66, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 66, "endColumn": 23}, {"ruleId": "1989", "severity": 1, "message": "2212", "line": 78, "column": 5, "nodeType": "1991", "endLine": 78, "endColumn": 7, "suggestions": "2213"}, {"ruleId": "1989", "severity": 1, "message": "2214", "line": 96, "column": 5, "nodeType": "1991", "endLine": 96, "endColumn": 28, "suggestions": "2215"}, {"ruleId": "1989", "severity": 1, "message": "2216", "line": 107, "column": 5, "nodeType": "1991", "endLine": 107, "endColumn": 48, "suggestions": "2217"}, {"ruleId": "1885", "severity": 1, "message": "2218", "line": 186, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 186, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2219", "line": 247, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 247, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2220", "line": 319, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 319, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2221", "line": 706, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 706, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2222", "line": 711, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 711, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2223", "line": 1, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2224", "line": 3, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2225", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1892", "line": 2, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2226", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2227", "line": 8, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2228", "line": 8, "column": 44, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 59}, {"ruleId": "1885", "severity": 1, "message": "2229", "line": 10, "column": 34, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 57}, {"ruleId": "1885", "severity": 1, "message": "2230", "line": 10, "column": 59, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 79}, {"ruleId": "1885", "severity": 1, "message": "2231", "line": 59, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2232", "line": 124, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 124, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1892", "line": 1, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2233", "line": 8, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2227", "line": 9, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2234", "line": 80, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 80, "endColumn": 58}, {"ruleId": "1989", "severity": 1, "message": "2235", "line": 86, "column": 8, "nodeType": "2053", "endLine": 90, "endColumn": 12}, {"ruleId": "1989", "severity": 1, "message": "2236", "line": 86, "column": 8, "nodeType": "2053", "endLine": 90, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2237", "line": 92, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2238", "line": 92, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 42}, {"ruleId": "2239", "severity": 1, "message": "2240", "line": 113, "column": 113, "nodeType": "2241", "messageId": "2242", "endLine": 113, "endColumn": 397}, {"ruleId": "1989", "severity": 1, "message": "2243", "line": 154, "column": 5, "nodeType": "1991", "endLine": 154, "endColumn": 38, "suggestions": "2244"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 154, "column": 6, "nodeType": "2041", "endLine": 154, "endColumn": 37}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 154, "column": 33, "nodeType": "2041", "messageId": "2042", "endLine": 154, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2231", "line": 156, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 156, "endColumn": 24}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 174, "column": 56, "nodeType": "2041", "messageId": "2042", "endLine": 174, "endColumn": 58}, {"ruleId": "1885", "severity": 1, "message": "2245", "line": 181, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 181, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2246", "line": 182, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 182, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2247", "line": 305, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 305, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2231", "line": 771, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 771, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2248", "line": 806, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 806, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2249", "line": 806, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 806, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2250", "line": 807, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 807, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2251", "line": 807, "column": 22, "nodeType": "1887", "messageId": "1888", "endLine": 807, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "2068", "line": 808, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 808, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2069", "line": 808, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 808, "endColumn": 48}, {"ruleId": "1885", "severity": 1, "message": "2252", "line": 809, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 809, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2253", "line": 809, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 809, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "1983", "line": 810, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 810, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2254", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2255", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2256", "line": 31, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2257", "line": 32, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 32, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2189", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "1896", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2193", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 9, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 11, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 12, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2259", "line": 14, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2152", "line": 16, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 18, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 19, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 20, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 21, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 21, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2264", "line": 22, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 23, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2266", "line": 24, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2267", "line": 25, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "1921", "line": 3, "column": 65, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 69}, {"ruleId": "1885", "severity": 1, "message": "2268", "line": 6, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2269", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2270", "line": 20, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2249", "line": 84, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 84, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2271", "line": 85, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2272", "line": 85, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2273", "line": 86, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 86, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2274", "line": 86, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 86, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2275", "line": 90, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 90, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2276", "line": 90, "column": 45, "nodeType": "1887", "messageId": "1888", "endLine": 90, "endColumn": 62}, {"ruleId": "1885", "severity": 1, "message": "2277", "line": 93, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 93, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2278", "line": 94, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 94, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1976", "line": 95, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 95, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "1977", "line": 96, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 96, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1978", "line": 97, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 97, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "1979", "line": 98, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 98, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "1980", "line": 99, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1981", "line": 100, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2279", "line": 101, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 101, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 102, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 102, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2281", "line": 103, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 104, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2282", "line": 105, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 105, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2283", "line": 107, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2284", "line": 108, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 108, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2285", "line": 115, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 115, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2286", "line": 116, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 116, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2287", "line": 118, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 118, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2288", "line": 119, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 119, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2289", "line": 120, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 120, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2290", "line": 121, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 121, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2291", "line": 122, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 122, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2292", "line": 123, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 123, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2293", "line": 132, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 132, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2294", "line": 137, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 137, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2159", "line": 139, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 139, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2166", "line": 140, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 140, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2295", "line": 141, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 141, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2296", "line": 144, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 144, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2164", "line": 145, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 145, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2297", "line": 146, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 146, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2298", "line": 170, "column": 5, "nodeType": "1991", "endLine": 170, "endColumn": 45, "suggestions": "2299"}, {"ruleId": "1885", "severity": 1, "message": "2300", "line": 221, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 221, "endColumn": 29}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 241, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 241, "endColumn": 26}, {"ruleId": "1989", "severity": 1, "message": "2301", "line": 315, "column": 7, "nodeType": "1991", "endLine": 315, "endColumn": 42, "suggestions": "2302"}, {"ruleId": "1885", "severity": 1, "message": "2303", "line": 339, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 339, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2304", "line": 340, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 340, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2305", "line": 488, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 488, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2306", "line": 491, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 491, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2307", "line": 500, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 500, "endColumn": 31}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 811, "column": 26, "nodeType": "2041", "messageId": "2042", "endLine": 811, "endColumn": 28}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 847, "column": 26, "nodeType": "2041", "messageId": "2042", "endLine": 847, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2308", "line": 1032, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1032, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2309", "line": 1036, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1036, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2310", "line": 1040, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1040, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2311", "line": 1044, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1044, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2312", "line": 1048, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1048, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2313", "line": 1052, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1052, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2314", "line": 1056, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1056, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2315", "line": 1060, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1060, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2316", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2317", "line": 13, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2318", "line": 15, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2319", "line": 79, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 79, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2320", "line": 81, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 81, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2321", "line": 82, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2322", "line": 83, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 83, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2323", "line": 84, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 84, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2003", "line": 88, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 88, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2324", "line": 89, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 89, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2325", "line": 91, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 91, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2005", "line": 92, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2006", "line": 93, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 93, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 94, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 94, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 104, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 19}, {"ruleId": "1989", "severity": 1, "message": "2326", "line": 209, "column": 7, "nodeType": "1991", "endLine": 209, "endColumn": 9, "suggestions": "2327"}, {"ruleId": "1989", "severity": 1, "message": "2328", "line": 244, "column": 7, "nodeType": "1991", "endLine": 244, "endColumn": 29, "suggestions": "2329"}, {"ruleId": "1989", "severity": 1, "message": "2330", "line": 249, "column": 7, "nodeType": "1991", "endLine": 249, "endColumn": 18, "suggestions": "2331"}, {"ruleId": "1989", "severity": 1, "message": "2332", "line": 292, "column": 7, "nodeType": "1991", "endLine": 292, "endColumn": 72, "suggestions": "2333"}, {"ruleId": "1885", "severity": 1, "message": "2282", "line": 331, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 331, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2334", "line": 334, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 334, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2335", "line": 463, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 463, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2336", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2337", "line": 6, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2338", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2339", "line": 7, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2340", "line": 8, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2341", "line": 9, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2005", "line": 13, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2006", "line": 14, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 15, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2342", "line": 17, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2343", "line": 18, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2344", "line": 18, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2345", "line": 19, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2346", "line": 96, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 96, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2347", "line": 97, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 97, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2348", "line": 100, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2349", "line": 101, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 101, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2350", "line": 109, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 109, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2351", "line": 129, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 129, "endColumn": 16}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 176, "column": 45, "nodeType": "2041", "messageId": "2042", "endLine": 176, "endColumn": 47}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 176, "column": 104, "nodeType": "2041", "messageId": "2042", "endLine": 176, "endColumn": 106}, {"ruleId": "1885", "severity": 1, "message": "2153", "line": 2, "column": 60, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 73}, {"ruleId": "1885", "severity": 1, "message": "2336", "line": 3, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2352", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2353", "line": 9, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2354", "line": 133, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 133, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2355", "line": 134, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 134, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2356", "line": 135, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 135, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2357", "line": 135, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 135, "endColumn": 52}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 137, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 137, "endColumn": 19}, {"ruleId": "1989", "severity": 1, "message": "2358", "line": 163, "column": 8, "nodeType": "1991", "endLine": 163, "endColumn": 10, "suggestions": "2359"}, {"ruleId": "1885", "severity": 1, "message": "2360", "line": 299, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 299, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2361", "line": 342, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 342, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2362", "line": 343, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 343, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2363", "line": 344, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 344, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2364", "line": 346, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 346, "endColumn": 20}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 465, "column": 22, "nodeType": "2041", "messageId": "2042", "endLine": 465, "endColumn": 24}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 465, "column": 53, "nodeType": "2041", "messageId": "2042", "endLine": 465, "endColumn": 55}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 465, "column": 89, "nodeType": "2041", "messageId": "2042", "endLine": 465, "endColumn": 91}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 465, "column": 125, "nodeType": "2041", "messageId": "2042", "endLine": 465, "endColumn": 127}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 467, "column": 29, "nodeType": "2041", "messageId": "2042", "endLine": 467, "endColumn": 31}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 467, "column": 56, "nodeType": "2041", "messageId": "2042", "endLine": 467, "endColumn": 58}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 467, "column": 88, "nodeType": "2041", "messageId": "2042", "endLine": 467, "endColumn": 90}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 467, "column": 120, "nodeType": "2041", "messageId": "2042", "endLine": 467, "endColumn": 122}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 469, "column": 29, "nodeType": "2041", "messageId": "2042", "endLine": 469, "endColumn": 31}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 469, "column": 64, "nodeType": "2041", "messageId": "2042", "endLine": 469, "endColumn": 66}, {"ruleId": "1885", "severity": 1, "message": "2365", "line": 111, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 111, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2292", "line": 152, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 152, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2017", "line": 153, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 153, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2366", "line": 159, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 159, "endColumn": 23}, {"ruleId": "2367", "severity": 1, "message": "2368", "line": 225, "column": 25, "nodeType": "1887", "messageId": "2369", "endLine": 225, "endColumn": 34, "suggestions": "2370"}, {"ruleId": "1989", "severity": 1, "message": "2371", "line": 231, "column": 5, "nodeType": "1991", "endLine": 231, "endColumn": 12, "suggestions": "2372"}, {"ruleId": "1989", "severity": 1, "message": "2373", "line": 237, "column": 5, "nodeType": "1991", "endLine": 237, "endColumn": 21, "suggestions": "2374"}, {"ruleId": "1989", "severity": 1, "message": "2375", "line": 472, "column": 5, "nodeType": "1991", "endLine": 472, "endColumn": 70, "suggestions": "2376"}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 547, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 547, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 548, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 548, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 549, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 549, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 550, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 550, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 554, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 554, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 555, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 555, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 556, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 556, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 557, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 557, "endColumn": 26}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 561, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 561, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 562, "column": 24, "nodeType": "2041", "messageId": "2042", "endLine": 562, "endColumn": 26}, {"ruleId": "1989", "severity": 1, "message": "2377", "line": 582, "column": 5, "nodeType": "1991", "endLine": 582, "endColumn": 64, "suggestions": "2378"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 582, "column": 6, "nodeType": "2095", "endLine": 582, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2379", "line": 591, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 591, "endColumn": 21}, {"ruleId": "1989", "severity": 1, "message": "2380", "line": 605, "column": 5, "nodeType": "1991", "endLine": 605, "endColumn": 47, "suggestions": "2381"}, {"ruleId": "1885", "severity": 1, "message": "2379", "line": 614, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 614, "endColumn": 21}, {"ruleId": "1989", "severity": 1, "message": "2380", "line": 627, "column": 5, "nodeType": "1991", "endLine": 627, "endColumn": 47, "suggestions": "2382"}, {"ruleId": "1989", "severity": 1, "message": "2383", "line": 1021, "column": 17, "nodeType": "1887", "endLine": 1021, "endColumn": 32}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 1227, "column": 43, "nodeType": "2041", "messageId": "2042", "endLine": 1227, "endColumn": 45}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 1232, "column": 78, "nodeType": "2041", "messageId": "2042", "endLine": 1232, "endColumn": 80}, {"ruleId": "1885", "severity": 1, "message": "2384", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2385", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2386", "line": 2, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2129", "line": 3, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2387", "line": 11, "column": 62, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 67}, {"ruleId": "1885", "severity": 1, "message": "2210", "line": 25, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2168", "line": 28, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2388", "line": 31, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2389", "line": 144, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 144, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2390", "line": 145, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 145, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2391", "line": 1, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 5, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 5}, {"ruleId": "1885", "severity": 1, "message": "2392", "line": 6, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2393", "line": 10, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2394", "line": 12, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 13, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2395", "line": 17, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2233", "line": 19, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2396", "line": 33, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 33, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2250", "line": 33, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 33, "endColumn": 44}, {"ruleId": "2397", "severity": 1, "message": "2398", "line": 95, "column": 2, "nodeType": "2399", "messageId": "2400", "endLine": 111, "endColumn": 4}, {"ruleId": "1885", "severity": 1, "message": "2401", "line": 132, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 132, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2402", "line": 135, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 135, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 136, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 136, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 137, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 137, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2403", "line": 139, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 139, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2277", "line": 140, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 140, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2404", "line": 141, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 141, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2405", "line": 144, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 144, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2406", "line": 145, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 145, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2407", "line": 146, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 146, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2408", "line": 147, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 147, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2409", "line": 148, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 148, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2410", "line": 149, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 149, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2287", "line": 150, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 150, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2286", "line": 151, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 151, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2285", "line": 152, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 152, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2411", "line": 155, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 155, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2412", "line": 158, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 158, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2413", "line": 159, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 159, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2414", "line": 160, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 160, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2203", "line": 161, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 161, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2163", "line": 162, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 162, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2415", "line": 165, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 165, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2416", "line": 166, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 166, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2366", "line": 168, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 168, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2417", "line": 173, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 173, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2017", "line": 174, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 174, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2418", "line": 185, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 185, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2419", "line": 185, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 185, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2420", "line": 187, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 187, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2421", "line": 187, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 187, "endColumn": 44}, {"ruleId": "2422", "severity": 1, "message": "2423", "line": 349, "column": 5, "nodeType": "2424", "messageId": "2425", "endLine": 349, "endColumn": 52}, {"ruleId": "1885", "severity": 1, "message": "2426", "line": 504, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 504, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2427", "line": 511, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 511, "endColumn": 21}, {"ruleId": "1989", "severity": 1, "message": "2428", "line": 668, "column": 5, "nodeType": "1991", "endLine": 668, "endColumn": 100, "suggestions": "2429"}, {"ruleId": "1989", "severity": 1, "message": "2430", "line": 775, "column": 5, "nodeType": "1991", "endLine": 775, "endColumn": 22, "suggestions": "2431"}, {"ruleId": "1885", "severity": 1, "message": "2432", "line": 869, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 869, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2433", "line": 876, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 876, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2384", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2434", "line": 1, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "2435", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2201", "line": 14, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2207", "line": 15, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2202", "line": 16, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2168", "line": 17, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2211", "line": 20, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2436", "line": 27, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2434", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2437", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2129", "line": 2, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 2, "column": 39, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2393", "line": 2, "column": 44, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 58}, {"ruleId": "1885", "severity": 1, "message": "2153", "line": 2, "column": 60, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 73}, {"ruleId": "1885", "severity": 1, "message": "2264", "line": 2, "column": 74, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 84}, {"ruleId": "1885", "severity": 1, "message": "2438", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2439", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2201", "line": 98, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 98, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2207", "line": 99, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2202", "line": 100, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2008", "line": 101, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 101, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2203", "line": 102, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 102, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2204", "line": 103, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2025", "line": 104, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2026", "line": 105, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 105, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2251", "line": 110, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 110, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 113, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 113, "endColumn": 29}, {"ruleId": "1989", "severity": 1, "message": "2440", "line": 172, "column": 12, "nodeType": "1991", "endLine": 172, "endColumn": 35, "suggestions": "2441"}, {"ruleId": "1885", "severity": 1, "message": "1892", "line": 1, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2122", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2225", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "1934", "line": 7, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2442", "line": 43, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 34}, {"ruleId": "1989", "severity": 1, "message": "2443", "line": 63, "column": 21, "nodeType": "2444", "endLine": 63, "endColumn": 111}, {"ruleId": "1885", "severity": 1, "message": "2445", "line": 2, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "2446", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2121", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2447", "line": 11, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2448", "line": 1, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2449", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "1923", "line": 1, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2449", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2450", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 40}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2451", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2451", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2264", "line": 2, "column": 50, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 60}, {"ruleId": "1885", "severity": 1, "message": "2438", "line": 29, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2192", "line": 34, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2417", "line": 64, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 64, "endColumn": 43}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 72, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 72, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2453", "line": 74, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 74, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2454", "line": 97, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 97, "endColumn": 22}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 106, "column": 34, "nodeType": "2041", "messageId": "2042", "endLine": 106, "endColumn": 36}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 154, "column": 44, "nodeType": "2041", "messageId": "2042", "endLine": 154, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "2455", "line": 173, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 173, "endColumn": 21}, {"ruleId": "1989", "severity": 1, "message": "2456", "line": 301, "column": 5, "nodeType": "1991", "endLine": 301, "endColumn": 50, "suggestions": "2457"}, {"ruleId": "1989", "severity": 1, "message": "2456", "line": 317, "column": 5, "nodeType": "1991", "endLine": 317, "endColumn": 18, "suggestions": "2458"}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 477, "column": 48, "nodeType": "2041", "messageId": "2042", "endLine": 477, "endColumn": 50}, {"ruleId": "1885", "severity": 1, "message": "2384", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2459", "line": 61, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2460", "line": 63, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 63, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2461", "line": 74, "column": 20, "nodeType": "1887", "messageId": "1888", "endLine": 74, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2462", "line": 308, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 308, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2464", "line": 6, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 22}, {"ruleId": "1989", "severity": 1, "message": "2465", "line": 332, "column": 8, "nodeType": "1991", "endLine": 332, "endColumn": 45, "suggestions": "2466"}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 44, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 53}, {"ruleId": "1885", "severity": 1, "message": "2467", "line": 4, "column": 46, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 65}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 4, "column": 67, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 75}, {"ruleId": "1885", "severity": 1, "message": "2438", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2468", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2201", "line": 30, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 30, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2469", "line": 31, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 34, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2470", "line": 44, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2471", "line": 45, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2472", "line": 46, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2473", "line": 47, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2474", "line": 51, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2475", "line": 51, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2476", "line": 52, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2477", "line": 53, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2478", "line": 56, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2479", "line": 57, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 57, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2480", "line": 57, "column": 20, "nodeType": "1887", "messageId": "1888", "endLine": 57, "endColumn": 32}, {"ruleId": "1989", "severity": 1, "message": "2481", "line": 65, "column": 5, "nodeType": "1991", "endLine": 65, "endColumn": 7, "suggestions": "2482"}, {"ruleId": "1885", "severity": 1, "message": "2483", "line": 93, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 93, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2484", "line": 97, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 97, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2485", "line": 124, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 124, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2486", "line": 132, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 132, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2487", "line": 136, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 136, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2488", "line": 150, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 150, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2489", "line": 153, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 153, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "1892", "line": 5, "column": 28, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2490", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2237", "line": 85, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2491", "line": 98, "column": 6, "nodeType": "1991", "endLine": 98, "endColumn": 8, "suggestions": "2492"}, {"ruleId": "1989", "severity": 1, "message": "2493", "line": 121, "column": 6, "nodeType": "1991", "endLine": 121, "endColumn": 32, "suggestions": "2494"}, {"ruleId": "1989", "severity": 1, "message": "2243", "line": 125, "column": 6, "nodeType": "1991", "endLine": 125, "endColumn": 40, "suggestions": "2495"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 125, "column": 7, "nodeType": "2041", "endLine": 125, "endColumn": 39}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 125, "column": 35, "nodeType": "2041", "messageId": "2042", "endLine": 125, "endColumn": 37}, {"ruleId": "1989", "severity": 1, "message": "2496", "line": 148, "column": 6, "nodeType": "1991", "endLine": 148, "endColumn": 33, "suggestions": "2497"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 148, "column": 7, "nodeType": "2046", "endLine": 148, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2498", "line": 156, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 156, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2499", "line": 2, "column": 14, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2500", "line": 16, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2501", "line": 19, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2502", "line": 22, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 20}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 43, "column": 100, "nodeType": "2041", "messageId": "2042", "endLine": 43, "endColumn": 102}, {"ruleId": "1885", "severity": 1, "message": "2503", "line": 4, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2504", "line": 4, "column": 32, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 45}, {"ruleId": "1885", "severity": 1, "message": "2505", "line": 10, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2506", "line": 65, "column": 12, "nodeType": "1887", "messageId": "1888", "endLine": 65, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2507", "line": 65, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 65, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2508", "line": 78, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 78, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2509", "line": 78, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 78, "endColumn": 39}, {"ruleId": "1989", "severity": 1, "message": "2510", "line": 120, "column": 6, "nodeType": "1991", "endLine": 120, "endColumn": 8, "suggestions": "2511"}, {"ruleId": "1885", "severity": 1, "message": "2512", "line": 157, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 157, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2513", "line": 280, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 280, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2514", "line": 296, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 296, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2515", "line": 461, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 461, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2516", "line": 462, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 462, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2517", "line": 467, "column": 3, "nodeType": "1991", "endLine": 467, "endColumn": 5, "suggestions": "2518"}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 2, "column": 80, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 2, "column": 105, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 111}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 2, "column": 113, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 121}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2522", "line": 2, "column": 168, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 180}, {"ruleId": "1885", "severity": 1, "message": "2523", "line": 2, "column": 182, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 199}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 4, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 4, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 4, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 13, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 14, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 15, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 16, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 17, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2532", "line": 24, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2533", "line": 25, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2534", "line": 26, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2535", "line": 27, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2536", "line": 28, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2537", "line": 29, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2538", "line": 30, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 30, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2539", "line": 40, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 40, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2540", "line": 41, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 41, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2541", "line": 43, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2542", "line": 45, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2245", "line": 47, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2543", "line": 94, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 94, "endColumn": 19}, {"ruleId": "1989", "severity": 1, "message": "2544", "line": 125, "column": 5, "nodeType": "1991", "endLine": 125, "endColumn": 7, "suggestions": "2545"}, {"ruleId": "1885", "severity": 1, "message": "2546", "line": 145, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 145, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2547", "line": 162, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 162, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2548", "line": 165, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 165, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2549", "line": 170, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 170, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2550", "line": 211, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 211, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2551", "line": 214, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 214, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2552", "line": 227, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 227, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2553", "line": 228, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 228, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2554", "line": 228, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 228, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2555", "line": 229, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 229, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2556", "line": 229, "column": 20, "nodeType": "1887", "messageId": "1888", "endLine": 229, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "1971", "line": 245, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 245, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1972", "line": 245, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 245, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2557", "line": 247, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 247, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2558", "line": 261, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 261, "endColumn": 36}, {"ruleId": "1989", "severity": 1, "message": "2559", "line": 281, "column": 4, "nodeType": "1991", "endLine": 281, "endColumn": 6, "suggestions": "2560"}, {"ruleId": "1885", "severity": 1, "message": "2558", "line": 334, "column": 12, "nodeType": "1887", "messageId": "1888", "endLine": 334, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2561", "line": 347, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 347, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2562", "line": 347, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 347, "endColumn": 45}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 2, "column": 80, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 2, "column": 105, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 111}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 2, "column": 113, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 121}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 2, "column": 168, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 175}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 4, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 4, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 4, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 8, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 9, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 10, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 11, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 12, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2563", "line": 13, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2564", "line": 14, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2565", "line": 15, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2566", "line": 22, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2567", "line": 31, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2229", "line": 32, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 32, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2539", "line": 35, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 35, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2540", "line": 36, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 36, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2541", "line": 38, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2568", "line": 39, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2569", "line": 40, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 40, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2570", "line": 42, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 42, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2571", "line": 43, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2572", "line": 44, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2573", "line": 45, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2574", "line": 46, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2575", "line": 47, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2576", "line": 48, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2577", "line": 49, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2578", "line": 50, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2579", "line": 51, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 58, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2501", "line": 60, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2543", "line": 75, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 75, "endColumn": 19}, {"ruleId": "1989", "severity": 1, "message": "2496", "line": 87, "column": 5, "nodeType": "1991", "endLine": 87, "endColumn": 45, "suggestions": "2580"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 87, "column": 6, "nodeType": "2095", "endLine": 87, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2581", "line": 106, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 106, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2582", "line": 106, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 106, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2583", "line": 107, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2584", "line": 107, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2585", "line": 108, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 108, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2162", "line": 109, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 109, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2586", "line": 109, "column": 18, "nodeType": "1887", "messageId": "1888", "endLine": 109, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2587", "line": 110, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 110, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2547", "line": 115, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 115, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2549", "line": 119, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 119, "endColumn": 25}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 134, "column": 11, "nodeType": "2041", "messageId": "2042", "endLine": 134, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2588", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2589", "line": 5, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2590", "line": 10, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2522", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "2523", "line": 2, "column": 41, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 58}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 72, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 88}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 90, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 96}, {"ruleId": "1885", "severity": 1, "message": "2591", "line": 9, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 6, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 9, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 10, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 11, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 12, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "2592", "line": 19, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2297", "line": 35, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 35, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2593", "line": 37, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 37, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2396", "line": 38, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2250", "line": 39, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2594", "line": 40, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 40, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2164", "line": 42, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 42, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 48, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2595", "line": 55, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 55, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2596", "line": 56, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2597", "line": 57, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 57, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2598", "line": 86, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 86, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2599", "line": 90, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 90, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2600", "line": 95, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 95, "endColumn": 33}, {"ruleId": "1989", "severity": 1, "message": "2601", "line": 195, "column": 5, "nodeType": "1991", "endLine": 195, "endColumn": 30, "suggestions": "2602"}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 3, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 5}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 4, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 9, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2588", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 32}, {"ruleId": "1885", "severity": 1, "message": "2589", "line": 5, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2603", "line": 9, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2604", "line": 9, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2590", "line": 9, "column": 32, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 41}, {"ruleId": "1885", "severity": 1, "message": "2605", "line": 9, "column": 43, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 51}, {"ruleId": "1885", "severity": 1, "message": "2606", "line": 9, "column": 53, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 63}, {"ruleId": "1885", "severity": 1, "message": "2607", "line": 9, "column": 65, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 77}, {"ruleId": "1885", "severity": 1, "message": "2608", "line": 9, "column": 79, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 90}, {"ruleId": "1885", "severity": 1, "message": "2609", "line": 9, "column": 92, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 102}, {"ruleId": "1885", "severity": 1, "message": "2610", "line": 9, "column": 104, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 116}, {"ruleId": "1885", "severity": 1, "message": "2611", "line": 9, "column": 118, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 129}, {"ruleId": "1885", "severity": 1, "message": "2612", "line": 9, "column": 131, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2613", "line": 15, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 16, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2614", "line": 17, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2404", "line": 18, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2615", "line": 19, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 20, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2403", "line": 23, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2616", "line": 24, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2617", "line": 25, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2618", "line": 26, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2619", "line": 27, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2620", "line": 28, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2621", "line": 29, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2622", "line": 30, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 30, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 34, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2543", "line": 62, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 62, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2599", "line": 82, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2623", "line": 83, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 83, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 3, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 3, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 3, "column": 80, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 3, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 3, "column": 105, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 111}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 3, "column": 113, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 121}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 3, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 3, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 3, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 5, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 5, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 5, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 6, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 8, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 9, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 10, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 11, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2539", "line": 19, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2541", "line": 22, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2624", "line": 24, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2625", "line": 25, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2626", "line": 26, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2627", "line": 27, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 31, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2543", "line": 36, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 36, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2628", "line": 104, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2629", "line": 105, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 105, "endColumn": 40}, {"ruleId": "1885", "severity": 1, "message": "2546", "line": 120, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 120, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2547", "line": 154, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 154, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2630", "line": 157, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 157, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 3, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 3, "column": 56, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 63}, {"ruleId": "1885", "severity": 1, "message": "2165", "line": 13, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2277", "line": 14, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2278", "line": 15, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1976", "line": 16, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "1978", "line": 18, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "1979", "line": 19, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "1980", "line": 20, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "1981", "line": 21, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 21, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2279", "line": 22, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 23, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2281", "line": 24, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 25, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2631", "line": 37, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 37, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2319", "line": 39, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2632", "line": 41, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 41, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2633", "line": 45, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2634", "line": 49, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2635", "line": 49, "column": 25, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2636", "line": 50, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2637", "line": 50, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2638", "line": 51, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2639", "line": 51, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2640", "line": 52, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2641", "line": 52, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 52}, {"ruleId": "1885", "severity": 1, "message": "2642", "line": 53, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2643", "line": 53, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "2644", "line": 3, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2645", "line": 4, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2646", "line": 5, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2647", "line": 6, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2437", "line": 9, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "2146", "line": 17, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2147", "line": 18, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 19, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2149", "line": 20, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 7}, {"ruleId": "1885", "severity": 1, "message": "2150", "line": 23, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2151", "line": 24, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2152", "line": 25, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 25, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 26, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "2337", "line": 43, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2648", "line": 44, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2649", "line": 50, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2650", "line": 51, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2651", "line": 53, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2652", "line": 54, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2653", "line": 55, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 55, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2654", "line": 56, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2165", "line": 58, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2201", "line": 59, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2469", "line": 60, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2159", "line": 62, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 62, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2160", "line": 68, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 68, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2161", "line": 69, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 69, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2162", "line": 75, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 75, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2655", "line": 77, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 77, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2005", "line": 80, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 80, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2006", "line": 81, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 81, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 82, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2656", "line": 83, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 83, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2657", "line": 84, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 84, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2658", "line": 85, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2175", "line": 87, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 87, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2173", "line": 89, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 89, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2177", "line": 91, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 91, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2659", "line": 92, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2170", "line": 94, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 94, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 101, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 101, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2279", "line": 101, "column": 22, "nodeType": "1887", "messageId": "1888", "endLine": 101, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 102, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 102, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2281", "line": 102, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 102, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2660", "line": 103, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2661", "line": 104, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 104, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2662", "line": 105, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 105, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2663", "line": 105, "column": 14, "nodeType": "1887", "messageId": "1888", "endLine": 105, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2171", "line": 106, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 106, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2664", "line": 106, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 106, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2364", "line": 107, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2665", "line": 107, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 107, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2597", "line": 118, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 118, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2666", "line": 118, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 118, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2667", "line": 125, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 125, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2668", "line": 125, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 125, "endColumn": 44}, {"ruleId": "1989", "severity": 1, "message": "2669", "line": 148, "column": 5, "nodeType": "1991", "endLine": 148, "endColumn": 60, "suggestions": "2670"}, {"ruleId": "1885", "severity": 1, "message": "2671", "line": 151, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 151, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2672", "line": 163, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 163, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2673", "line": 167, "column": 5, "nodeType": "1991", "endLine": 167, "endColumn": 60, "suggestions": "2674"}, {"ruleId": "1885", "severity": 1, "message": "2675", "line": 169, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 169, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2178", "line": 203, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 203, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2179", "line": 207, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 207, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 56, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 65}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 2, "column": 67, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 75}, {"ruleId": "1885", "severity": 1, "message": "2129", "line": 2, "column": 77, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 83}, {"ruleId": "1885", "severity": 1, "message": "2467", "line": 13, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2676", "line": 47, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 48}, {"ruleId": "1885", "severity": 1, "message": "2360", "line": 59, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 39}, {"ruleId": "1885", "severity": 1, "message": "2677", "line": 68, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 68, "endColumn": 41}, {"ruleId": "1885", "severity": 1, "message": "2678", "line": 74, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 74, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2384", "line": 1, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 4, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 4, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 4, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2679", "line": 23, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 23, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2014", "line": 24, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 24, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2680", "line": 26, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 26, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2010", "line": 27, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2681", "line": 28, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2682", "line": 29, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2304", "line": 85, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2336", "line": 4, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2005", "line": 11, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2006", "line": 12, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2004", "line": 13, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2342", "line": 21, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 21, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2343", "line": 22, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2344", "line": 22, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 44}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 82, "column": 22, "nodeType": "2041", "messageId": "2042", "endLine": 82, "endColumn": 24}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 82, "column": 53, "nodeType": "2041", "messageId": "2042", "endLine": 82, "endColumn": 55}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 85, "column": 36, "nodeType": "2041", "messageId": "2042", "endLine": 85, "endColumn": 38}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 85, "column": 63, "nodeType": "2041", "messageId": "2042", "endLine": 85, "endColumn": 65}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 88, "column": 36, "nodeType": "2041", "messageId": "2042", "endLine": 88, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2346", "line": 95, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 95, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2347", "line": 96, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 96, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2348", "line": 99, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2349", "line": 100, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2350", "line": 108, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 108, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2351", "line": 128, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 128, "endColumn": 16}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 268, "column": 45, "nodeType": "2041", "messageId": "2042", "endLine": 268, "endColumn": 47}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 268, "column": 104, "nodeType": "2041", "messageId": "2042", "endLine": 268, "endColumn": 106}, {"ruleId": "1885", "severity": 1, "message": "2683", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2392", "line": 4, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 8, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2684", "line": 15, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2685", "line": 15, "column": 30, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2686", "line": 15, "column": 46, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 60}, {"ruleId": "1885", "severity": 1, "message": "2687", "line": 15, "column": 62, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2688", "line": 15, "column": 80, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 96}, {"ruleId": "1885", "severity": 1, "message": "2689", "line": 17, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2690", "line": 17, "column": 35, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2154", "line": 17, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 55}, {"ruleId": "1885", "severity": 1, "message": "2691", "line": 22, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2692", "line": 58, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2401", "line": 65, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 65, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "2693", "line": 66, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 66, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2404", "line": 72, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 72, "endColumn": 8}, {"ruleId": "1885", "severity": 1, "message": "2403", "line": 73, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 73, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2617", "line": 74, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 74, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 75, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 75, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2694", "line": 76, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 76, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2411", "line": 77, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 77, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2695", "line": 78, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 78, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2412", "line": 79, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 79, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2407", "line": 80, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 80, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2696", "line": 81, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 81, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2619", "line": 82, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2163", "line": 83, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 83, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2158", "line": 84, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 84, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2697", "line": 87, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 87, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2698", "line": 89, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 89, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2164", "line": 91, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 91, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2169", "line": 93, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 93, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2699", "line": 99, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2700", "line": 99, "column": 21, "nodeType": "1887", "messageId": "1888", "endLine": 99, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2701", "line": 103, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2702", "line": 103, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2703", "line": 160, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 160, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2704", "line": 163, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 163, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2705", "line": 169, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 169, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2706", "line": 172, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 172, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2707", "line": 182, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 182, "endColumn": 21}, {"ruleId": "1989", "severity": 1, "message": "2708", "line": 335, "column": 5, "nodeType": "1991", "endLine": 335, "endColumn": 18, "suggestions": "2709"}, {"ruleId": "1885", "severity": 1, "message": "2710", "line": 337, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 337, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2414", "line": 586, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 586, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2250", "line": 586, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 586, "endColumn": 60}, {"ruleId": "1885", "severity": 1, "message": "2203", "line": 586, "column": 62, "nodeType": "1887", "messageId": "1888", "endLine": 586, "endColumn": 67}, {"ruleId": "1885", "severity": 1, "message": "2163", "line": 586, "column": 69, "nodeType": "1887", "messageId": "1888", "endLine": 586, "endColumn": 85}, {"ruleId": "1885", "severity": 1, "message": "2711", "line": 776, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 776, "endColumn": 17}, {"ruleId": "1989", "severity": 1, "message": "2712", "line": 914, "column": 3, "nodeType": "1991", "endLine": 914, "endColumn": 19, "suggestions": "2713"}, {"ruleId": "1885", "severity": 1, "message": "2714", "line": 988, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 988, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2715", "line": 997, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 997, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2394", "line": 11, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2336", "line": 14, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 14, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2716", "line": 67, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 67, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2717", "line": 180, "column": 86, "nodeType": "1887", "messageId": "1888", "endLine": 180, "endColumn": 101}, {"ruleId": "1885", "severity": 1, "message": "2412", "line": 184, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 184, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2718", "line": 563, "column": 5, "nodeType": "1991", "endLine": 563, "endColumn": 40, "suggestions": "2719"}, {"ruleId": "1989", "severity": 1, "message": "2720", "line": 586, "column": 6, "nodeType": "1991", "endLine": 586, "endColumn": 42, "suggestions": "2721"}, {"ruleId": "1989", "severity": 1, "message": "2722", "line": 600, "column": 6, "nodeType": "1991", "endLine": 600, "endColumn": 50, "suggestions": "2723"}, {"ruleId": "1989", "severity": 1, "message": "2724", "line": 877, "column": 5, "nodeType": "1991", "endLine": 877, "endColumn": 160, "suggestions": "2725"}, {"ruleId": "1989", "severity": 1, "message": "2726", "line": 945, "column": 5, "nodeType": "1991", "endLine": 945, "endColumn": 110, "suggestions": "2727"}, {"ruleId": "1989", "severity": 1, "message": "2728", "line": 975, "column": 5, "nodeType": "1991", "endLine": 975, "endColumn": 34, "suggestions": "2729"}, {"ruleId": "1989", "severity": 1, "message": "2730", "line": 993, "column": 5, "nodeType": "1991", "endLine": 993, "endColumn": 34, "suggestions": "2731"}, {"ruleId": "1989", "severity": 1, "message": "2730", "line": 1007, "column": 5, "nodeType": "1991", "endLine": 1007, "endColumn": 34, "suggestions": "2732"}, {"ruleId": "1989", "severity": 1, "message": "2730", "line": 1010, "column": 5, "nodeType": "1991", "endLine": 1010, "endColumn": 40, "suggestions": "2733"}, {"ruleId": "1885", "severity": 1, "message": "2734", "line": 1220, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1220, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2735", "line": 1223, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 1223, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 79}, {"ruleId": "1885", "severity": 1, "message": "1936", "line": 15, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 9}, {"ruleId": "1885", "severity": 1, "message": "2736", "line": 18, "column": 48, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 76}, {"ruleId": "1885", "severity": 1, "message": "2737", "line": 18, "column": 78, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 85}, {"ruleId": "1885", "severity": 1, "message": "2270", "line": 20, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2738", "line": 52, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2739", "line": 54, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2740", "line": 59, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2698", "line": 59, "column": 18, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2741", "line": 60, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "1971", "line": 61, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "1972", "line": 61, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2675", "line": 85, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2549", "line": 92, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 92, "endColumn": 25}, {"ruleId": "1989", "severity": 1, "message": "2742", "line": 183, "column": 5, "nodeType": "1991", "endLine": 183, "endColumn": 52, "suggestions": "2743"}, {"ruleId": "1989", "severity": 1, "message": "2045", "line": 183, "column": 6, "nodeType": "2095", "endLine": 183, "endColumn": 51}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 92, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 102}, {"ruleId": "1885", "severity": 1, "message": "2744", "line": 76, "column": 19, "nodeType": "1887", "messageId": "1888", "endLine": 76, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2437", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2264", "line": 2, "column": 36, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 2, "column": 59, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 67}, {"ruleId": "1885", "severity": 1, "message": "2265", "line": 2, "column": 77, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 84}, {"ruleId": "1885", "severity": 1, "message": "2745", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2746", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2747", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2748", "line": 9, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2434", "line": 1, "column": 29, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 37}, {"ruleId": "1885", "severity": 1, "message": "2384", "line": 1, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2189", "line": 1, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 59}, {"ruleId": "1885", "severity": 1, "message": "2391", "line": 1, "column": 61, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 67}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2129", "line": 2, "column": 56, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 62}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2260", "line": 2, "column": 80, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2262", "line": 2, "column": 105, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 111}, {"ruleId": "1885", "severity": 1, "message": "2263", "line": 2, "column": 113, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 121}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2438", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2225", "line": 4, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 4, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 4, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 4, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 7, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 8, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 9, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 10, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 11, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2563", "line": 12, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2749", "line": 19, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "1918", "line": 2, "column": 64, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 78}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2151", "line": 2, "column": 177, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 193}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 4, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 4, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 4, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 5, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 7, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 8, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 9, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 10, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 11, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2533", "line": 27, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2534", "line": 28, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2535", "line": 29, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 29, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2536", "line": 30, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 30, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2539", "line": 38, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 38, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2540", "line": 39, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2541", "line": 41, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 41, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2568", "line": 42, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 42, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2569", "line": 43, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2750", "line": 44, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2570", "line": 45, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2572", "line": 47, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2573", "line": 48, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 33}, {"ruleId": "1885", "severity": 1, "message": "2574", "line": 49, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2576", "line": 51, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 30}, {"ruleId": "1885", "severity": 1, "message": "2577", "line": 52, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2578", "line": 53, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 53, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2579", "line": 54, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 58, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2751", "line": 152, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 152, "endColumn": 40}, {"ruleId": "1885", "severity": 1, "message": "2752", "line": 153, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 153, "endColumn": 41}, {"ruleId": "1885", "severity": 1, "message": "2753", "line": 154, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 154, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "1971", "line": 156, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 156, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2754", "line": 185, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 185, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2547", "line": 220, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 220, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2548", "line": 223, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 223, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2549", "line": 228, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 228, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2079", "line": 245, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 245, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2581", "line": 249, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 249, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2587", "line": 254, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 254, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2755", "line": 263, "column": 6, "nodeType": "1991", "endLine": 263, "endColumn": 8, "suggestions": "2756"}, {"ruleId": "1885", "severity": 1, "message": "2757", "line": 298, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 298, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2519", "line": 1, "column": 17, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2391", "line": 1, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 55}, {"ruleId": "1885", "severity": 1, "message": "2758", "line": 1, "column": 69, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 80}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2261", "line": 2, "column": 93, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 103}, {"ruleId": "1885", "severity": 1, "message": "2520", "line": 2, "column": 123, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 140}, {"ruleId": "1885", "severity": 1, "message": "2148", "line": 2, "column": 142, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 158}, {"ruleId": "1885", "severity": 1, "message": "2521", "line": 2, "column": 160, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 166}, {"ruleId": "1885", "severity": 1, "message": "2392", "line": 2, "column": 195, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 212}, {"ruleId": "1885", "severity": 1, "message": "2524", "line": 5, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 49}, {"ruleId": "1885", "severity": 1, "message": "2525", "line": 5, "column": 51, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 71}, {"ruleId": "1885", "severity": 1, "message": "2526", "line": 5, "column": 73, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 91}, {"ruleId": "1885", "severity": 1, "message": "2527", "line": 6, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 31}, {"ruleId": "1885", "severity": 1, "message": "2532", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2533", "line": 9, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2534", "line": 10, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2535", "line": 11, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 11, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2536", "line": 12, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2537", "line": 13, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2528", "line": 15, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2529", "line": 16, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2530", "line": 17, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 17, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2531", "line": 18, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 18, "endColumn": 10}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 19, "column": 5, "nodeType": "1887", "messageId": "1888", "endLine": 19, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2746", "line": 34, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2759", "line": 44, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2539", "line": 45, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2540", "line": 46, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2541", "line": 48, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2568", "line": 49, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2569", "line": 50, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2750", "line": 51, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2570", "line": 52, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2572", "line": 54, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2573", "line": 55, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 55, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2574", "line": 56, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2576", "line": 58, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2577", "line": 59, "column": 6, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2578", "line": 60, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2579", "line": 61, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 61, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2760", "line": 63, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 63, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2452", "line": 66, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 66, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2751", "line": 89, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 89, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2752", "line": 90, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 90, "endColumn": 43}, {"ruleId": "1885", "severity": 1, "message": "2753", "line": 91, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 91, "endColumn": 46}, {"ruleId": "1989", "severity": 1, "message": "2761", "line": 112, "column": 5, "nodeType": "1991", "endLine": 112, "endColumn": 52, "suggestions": "2762"}, {"ruleId": "1885", "severity": 1, "message": "2547", "line": 117, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 117, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2548", "line": 120, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 120, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2549", "line": 125, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 125, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2763", "line": 135, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 135, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2079", "line": 175, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 175, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2764", "line": 183, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 183, "endColumn": 20}, {"ruleId": "1989", "severity": 1, "message": "2755", "line": 188, "column": 4, "nodeType": "1991", "endLine": 188, "endColumn": 6, "suggestions": "2765"}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 193, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 193, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 194, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 194, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 194, "column": 39, "nodeType": "2041", "messageId": "2042", "endLine": 194, "endColumn": 41}, {"ruleId": "2039", "severity": 1, "message": "2040", "line": 213, "column": 19, "nodeType": "2041", "messageId": "2042", "endLine": 213, "endColumn": 21}, {"ruleId": "2039", "severity": 1, "message": "2061", "line": 226, "column": 20, "nodeType": "2041", "messageId": "2042", "endLine": 226, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2766", "line": 279, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 279, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "1971", "line": 307, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 307, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2754", "line": 334, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 334, "endColumn": 23}, {"ruleId": "1989", "severity": 1, "message": "2767", "line": 371, "column": 4, "nodeType": "1991", "endLine": 371, "endColumn": 6, "suggestions": "2768"}, {"ruleId": "1885", "severity": 1, "message": "2188", "line": 2, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "2258", "line": 2, "column": 38, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2280", "line": 9, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2279", "line": 9, "column": 22, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 36}, {"ruleId": "1885", "severity": 1, "message": "1995", "line": 10, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2281", "line": 10, "column": 26, "nodeType": "1887", "messageId": "1888", "endLine": 10, "endColumn": 44}, {"ruleId": "1885", "severity": 1, "message": "2661", "line": 12, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2741", "line": 12, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 12, "endColumn": 46}, {"ruleId": "1885", "severity": 1, "message": "2662", "line": 13, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2663", "line": 13, "column": 14, "nodeType": "1887", "messageId": "1888", "endLine": 13, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2364", "line": 15, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 15, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2597", "line": 16, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2769", "line": 28, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 28, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2770", "line": 3, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 3, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2532", "line": 6, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 32, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 42}, {"ruleId": "1885", "severity": 1, "message": "2463", "line": 2, "column": 44, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 53}, {"ruleId": "1885", "severity": 1, "message": "2467", "line": 4, "column": 46, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 65}, {"ruleId": "1885", "severity": 1, "message": "1916", "line": 4, "column": 67, "nodeType": "1887", "messageId": "1888", "endLine": 4, "endColumn": 75}, {"ruleId": "1885", "severity": 1, "message": "2771", "line": 8, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2692", "line": 16, "column": 13, "nodeType": "1887", "messageId": "1888", "endLine": 16, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2772", "line": 31, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2294", "line": 33, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 33, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2773", "line": 43, "column": 6, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 34}, {"ruleId": "1885", "severity": 1, "message": "2774", "line": 85, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 85, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2488", "line": 95, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 95, "endColumn": 26}, {"ruleId": "1885", "severity": 1, "message": "2747", "line": 5, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2748", "line": 6, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2775", "line": 7, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2776", "line": 27, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 27, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2777", "line": 34, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 34, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2459", "line": 56, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 56, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2460", "line": 58, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 58, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2778", "line": 80, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 80, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2779", "line": 82, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2780", "line": 82, "column": 23, "nodeType": "1887", "messageId": "1888", "endLine": 82, "endColumn": 38}, {"ruleId": "1885", "severity": 1, "message": "2781", "line": 133, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 133, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2782", "line": 290, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 290, "endColumn": 28}, {"ruleId": "1989", "severity": 1, "message": "2783", "line": 344, "column": 5, "nodeType": "1991", "endLine": 344, "endColumn": 22, "suggestions": "2784"}, {"ruleId": "1885", "severity": 1, "message": "2683", "line": 1, "column": 58, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 67}, {"ruleId": "1885", "severity": 1, "message": "2437", "line": 2, "column": 15, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2190", "line": 2, "column": 33, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 43}, {"ruleId": "1885", "severity": 1, "message": "2691", "line": 5, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2145", "line": 6, "column": 41, "nodeType": "1887", "messageId": "1888", "endLine": 6, "endColumn": 53}, {"ruleId": "1885", "severity": 1, "message": "2689", "line": 7, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2154", "line": 7, "column": 16, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 22}, {"ruleId": "1885", "severity": 1, "message": "2785", "line": 7, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 29}, {"ruleId": "1885", "severity": 1, "message": "2786", "line": 7, "column": 31, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2316", "line": 7, "column": 37, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 47}, {"ruleId": "1885", "severity": 1, "message": "2690", "line": 7, "column": 49, "nodeType": "1887", "messageId": "1888", "endLine": 7, "endColumn": 61}, {"ruleId": "1885", "severity": 1, "message": "2438", "line": 8, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 8, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2746", "line": 9, "column": 8, "nodeType": "1887", "messageId": "1888", "endLine": 9, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2787", "line": 44, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2788", "line": 45, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 45, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2002", "line": 46, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2789", "line": 47, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 47, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2323", "line": 48, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2790", "line": 49, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 18}, {"ruleId": "1885", "severity": 1, "message": "2322", "line": 50, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 50, "endColumn": 16}, {"ruleId": "1885", "severity": 1, "message": "2321", "line": 51, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 51, "endColumn": 15}, {"ruleId": "1885", "severity": 1, "message": "2791", "line": 52, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 52, "endColumn": 12}, {"ruleId": "1885", "severity": 1, "message": "2319", "line": 54, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 54, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2792", "line": 55, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 55, "endColumn": 24}, {"ruleId": "1885", "severity": 1, "message": "2401", "line": 59, "column": 4, "nodeType": "1887", "messageId": "1888", "endLine": 59, "endColumn": 11}, {"ruleId": "1885", "severity": 1, "message": "2150", "line": 2, "column": 2, "nodeType": "1887", "messageId": "1888", "endLine": 2, "endColumn": 14}, {"ruleId": "1885", "severity": 1, "message": "2793", "line": 20, "column": 24, "nodeType": "1887", "messageId": "1888", "endLine": 20, "endColumn": 35}, {"ruleId": "1885", "severity": 1, "message": "2291", "line": 42, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 42, "endColumn": 21}, {"ruleId": "1885", "severity": 1, "message": "2396", "line": 43, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 43, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2166", "line": 44, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 44, "endColumn": 28}, {"ruleId": "1885", "severity": 1, "message": "2794", "line": 46, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 46, "endColumn": 13}, {"ruleId": "1885", "severity": 1, "message": "2251", "line": 48, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 48, "endColumn": 17}, {"ruleId": "1885", "severity": 1, "message": "2294", "line": 49, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 49, "endColumn": 20}, {"ruleId": "1885", "severity": 1, "message": "2275", "line": 60, "column": 10, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2276", "line": 60, "column": 45, "nodeType": "1887", "messageId": "1888", "endLine": 60, "endColumn": 62}, {"ruleId": "1885", "severity": 1, "message": "2795", "line": 100, "column": 11, "nodeType": "1887", "messageId": "1888", "endLine": 100, "endColumn": 27}, {"ruleId": "1885", "severity": 1, "message": "2796", "line": 103, "column": 12, "nodeType": "1887", "messageId": "1888", "endLine": 103, "endColumn": 26}, {"ruleId": "1989", "severity": 1, "message": "2493", "line": 128, "column": 5, "nodeType": "1991", "endLine": 128, "endColumn": 155, "suggestions": "2797"}, {"ruleId": "1885", "severity": 1, "message": "2445", "line": 1, "column": 27, "nodeType": "1887", "messageId": "1888", "endLine": 1, "endColumn": 41}, {"ruleId": "1885", "severity": 1, "message": "2798", "line": 22, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 22, "endColumn": 23}, {"ruleId": "1885", "severity": 1, "message": "2799", "line": 31, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 31, "endColumn": 19}, {"ruleId": "1885", "severity": 1, "message": "2800", "line": 40, "column": 7, "nodeType": "1887", "messageId": "1888", "endLine": 40, "endColumn": 25}, {"ruleId": "1885", "severity": 1, "message": "2801", "line": 5, "column": 3, "nodeType": "1887", "messageId": "1888", "endLine": 5, "endColumn": 24}, {"ruleId": "1989", "severity": 1, "message": "2802", "line": 121, "column": 6, "nodeType": "1991", "endLine": 121, "endColumn": 26, "suggestions": "2803"}, {"ruleId": "1885", "severity": 1, "message": "2804", "line": 39, "column": 9, "nodeType": "1887", "messageId": "1888", "endLine": 39, "endColumn": 14}, "@typescript-eslint/no-unused-vars", "'GuidePopup' is defined but never used.", "Identifier", "unusedVar", "'Rte' is defined but never used.", "'isAppReady' is assigned a value but never used.", "'LoginUserInfo' is defined but never used.", "'useMemo' is defined but never used.", "'Steps' is defined but never used.", "'PopupList' is defined but never used.", "'BUTTON_DEFAULT_VALUE' is defined but never used.", "'stopScraping' is defined but never used.", "'addicon' is defined but never used.", "'touricon' is defined but never used.", "'ProductToursicon' is defined but never used.", "'Tooltipsicon' is defined but never used.", "'announcementicon' is defined but never used.", "'Bannersicon' is defined but never used.", "'Checklisticon' is defined but never used.", "'Hotspoticon' is defined but never used.", "'Surveyicon' is defined but never used.", "'Announcementsicon' is defined but never used.", "'bannersicon' is defined but never used.", "'tooltipicon' is defined but never used.", "'checklisticon' is defined but never used.", "'hotspotsicon' is defined but never used.", "'surveysicon' is defined but never used.", "'settingsicon' is defined but never used.", "'undoicon' is defined but never used.", "'redoicon' is defined but never used.", "'shareicon' is defined but never used.", "'editicon' is defined but never used.", "'Outlet' is defined but never used.", "'InputAdornment' is defined but never used.", "'FormHelperText' is defined but never used.", "'List' is defined but never used.", "'Step' is defined but never used.", "'guideSetting' is defined but never used.", "'JSEncrypt' is defined but never used.", "'GetUserDetailsById' is defined but never used.", "'UserLogin' is defined but never used.", "'VisibilityOff' is defined but never used.", "'Visibility' is defined but never used.", "'initialsData' is defined but never used.", "'EditIcon' is defined but never used.", "'TooltipUserview' is defined but never used.", "'SubmitUpdateGuid' is defined but never used.", "'PageInteractions' is defined but never used.", "'ElementsSettings' is defined but never used.", "'DrawerState' is defined but never used.", "'Checklist' is defined but never used.", "'Padding' is defined but never used.", "'CheckIcon' is defined but never used.", "'TooltipPreview' is defined but never used.", "'DismissData' is defined but never used.", "'Canvas' is defined but never used.", "'Design' is defined but never used.", "'Advanced' is defined but never used.", "'Hotspot' is defined but never used.", "'stepId' is defined but never used.", "'userId' is defined but never used.", "'loginUserData' is defined but never used.", "'setIsGuidesListOpen' is assigned a value but never used.", "'setIsInHomeScreen' is assigned a value but never used.", "'setIsAnnouncementListOpen' is assigned a value but never used.", "'setIsBannerslistOpen' is assigned a value but never used.", "'selectedTemplated' is assigned a value but never used.", "'setSelectedTemplated' is assigned a value but never used.", "'errorInStepName' is assigned a value but never used.", "'showTextField' is assigned a value but never used.", "'signOut' is assigned a value but never used.", "'selectedElement' is assigned a value but never used.", "'setSelectedElement' is assigned a value but never used.", "'showPassword' is assigned a value but never used.", "'setShowPassword' is assigned a value but never used.", "'password' is assigned a value but never used.", "'setPassword' is assigned a value but never used.", "'loginUserInfo' is assigned a value but never used.", "'setLoginUserInfo' is assigned a value but never used.", "'setresponse' is assigned a value but never used.", "'isTooltipPopupOpen' is assigned a value but never used.", "'setIsTooltipPopupOpen' is assigned a value but never used.", "'email' is assigned a value but never used.", "'setEmail' is assigned a value but never used.", "'loginUserDetails' is assigned a value but never used.", "'setUserDetails' is assigned a value but never used.", "'error' is assigned a value but never used.", "'setError' is assigned a value but never used.", "'isSelectingElement' is assigned a value but never used.", "'selectedElementDetails' is assigned a value but never used.", "'setSelectedElementDetails' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setPosition' is assigned a value but never used.", "'radius' is assigned a value but never used.", "'setRadius' is assigned a value but never used.", "'borderSize' is assigned a value but never used.", "'setBorderSize' is assigned a value but never used.", "'announcementData' is assigned a value but never used.", "'currentUrl' is assigned a value but never used.", "'isBannerPopupOpen' is assigned a value but never used.", "'i18nInitialized' is assigned a value but never used.", "'setI18nInitialized' is assigned a value but never used.", "'hashValue' is assigned a value but never used.", "'setHashValue' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has an unnecessary dependency: 'window.location.href'. Either exclude it or remove the dependency array. Outer scope values like 'window.location.href' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["2805"], "'fit' is assigned a value but never used.", "'fill' is assigned a value but never used.", "'backgroundColor' is assigned a value but never used.", "'sectionHeight' is assigned a value but never used.", "'setSectionHeight' is assigned a value but never used.", "'guidedatas' is assigned a value but never used.", "'setGuideDataS' is assigned a value but never used.", "'hotspotPopup' is assigned a value but never used.", "'setHotspotPopup' is assigned a value but never used.", "'textvaluess' is assigned a value but never used.", "'preview' is assigned a value but never used.", "'btnBorderColor' is assigned a value but never used.", "'btnBgColor' is assigned a value but never used.", "'btnTextColor' is assigned a value but never used.", "'isTooltipPopup' is assigned a value but never used.", "'setSteps' is assigned a value but never used.", "'newCurrentStep' is assigned a value but never used.", "'updateCanvasInTooltip' is assigned a value but never used.", "'hotspbgcolor' is assigned a value but never used.", "'setHotspBgColor' is assigned a value but never used.", "'setHotspotDataOnEdit' is assigned a value but never used.", "'openTooltip' is assigned a value but never used.", "'setXpathToTooltipMetaData' is assigned a value but never used.", "'setAxisData' is assigned a value but never used.", "'axisData' is assigned a value but never used.", "'setAutoPosition' is assigned a value but never used.", "'targetURL' is assigned a value but never used.", "'elementButtonName' is assigned a value but never used.", "'setElementButtonName' is assigned a value but never used.", "'isSaveClicked' is assigned a value but never used.", "'setbtnidss' is assigned a value but never used.", "'setPulseAnimationsH' is assigned a value but never used.", "'tooltipCount' is assigned a value but never used.", "'HotspotGuideDetails' is assigned a value but never used.", "'TooltipGuideDetailsNew' is assigned a value but never used.", "'editClicked' is assigned a value but never used.", "'textArray' is assigned a value but never used.", "'setTextArray' is assigned a value but never used.", "'setIsALTKeywordEnabled' is assigned a value but never used.", "'setDrawerActiveMenu' is assigned a value but never used.", "'setDrawerSearchText' is assigned a value but never used.", "'setInteractionData' is assigned a value but never used.", "'syncAIAnnouncementCanvasSettings' is assigned a value but never used.", "'ele4' is assigned a value but never used.", "'targetElement' is assigned a value but never used.", "'setHotspotClicked' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "React Hook useEffect has missing dependencies: 'fetchGuideDetails' and 'hotspot'. Either include them or remove the dependency array.", ["2806"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "MemberExpression", "React Hook useEffect has a missing dependency: 'setDesignPopup'. Either include it or remove the dependency array.", ["2807"], "'screenWidth' is assigned a value but never used.", "'dialogWidth' is assigned a value but never used.", "'handlechangeStep' is assigned a value but never used.", "The 'initialState' object makes the dependencies of useEffect Hook (at line 1053) change on every render. To fix this, wrap the initialization of 'initialState' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'determineCurrentScreen'. Either include it or remove the dependency array.", ["2808"], "React Hook useEffect has a missing dependency: 'errors'. Either include it or remove the dependency array. Outer scope values like 'selectedStepType' aren't valid dependencies because mutating them doesn't re-render the component.", ["2809"], "React Hook useEffect has missing dependencies: 'bannerPopup', 'clearBannerButtonDetials', 'currentGuideId', 'selectedTemplate', 'selectedTemplateTour', 'setBannerButtonSelected', 'setBannerPopup', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsHotspotCreationBuilderOpen', 'setIsTooltipCreationBuilderOpen', 'updateButtonContainerOnReload', and 'updateRTEContainerOnReload'. Either include them or remove the dependency array.", ["2810"], "'setCount' is assigned a value but never used.", "Expected '===' and instead saw '=='.", "'handleGuidesSettingsclick' is assigned a value but never used.", "'synchronizePreviewData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'elementSelected', 'isShowIcon', 'resetALTKeywordForNewTooltip', 'setElementSelected', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2811"], "'handleElementSelectionToggle' is assigned a value but never used.", "'userInfoObj' is assigned a value but never used.", "'isAnnouncementOpen' is assigned a value but never used.", "'setAnnouncementOpen' is assigned a value but never used.", "'aiCreationComplete' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setSettingAnchorEl'. Either include it or remove the dependency array.", ["2812"], "'defaultButtonSection' is assigned a value but never used.", "'responseData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setIsUnSavedChanges' and 'stepCreation'. Either include them or remove the dependency array.", ["2813"], "'handleNewInteractionClick' is assigned a value but never used.", "Assignments to the 'accountId' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'handleEditClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'userType'. Either include it or remove the dependency array.", ["2814"], "'editstepNameClicked' is assigned a value but never used.", "'setEditStepNameClicked' is assigned a value but never used.", "'handleNextClick' is assigned a value but never used.", "'isValid' is assigned a value but never used.", "'handleEventChange' is assigned a value but never used.", "'isGuideNameUnique' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'updatedGuideData.GuideStep'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData.GuideStep' aren't valid dependencies because mutating them doesn't re-render the component.", ["2815"], "'getAlignment' is defined but never used.", "'popupVisible' is assigned a value but never used.", "'triggerType' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentGuide?.GuideStep'. Either include it or remove the dependency array.", ["2816"], "ChainExpression", "'customButton' is assigned a value but never used.", "'groupedButtons' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'cleanupDuplicateSteps', 'createWithAI', 'currentGuideId', 'interactionData', and 'resetHeightofBanner'. Either include them or remove the dependency array.", ["2817"], "'isDisabled' is assigned a value but never used.", "'guideType' is assigned a value but never used.", "'guideSteps' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetGuideName', 'cleanupDuplicateSteps', 'createWithAI', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'overlayEnabled', 'pageinteraction', 'progress', 'resetHeightofBanner', 'setBannerPopup', 'setBposition', 'setDismiss', 'setIsGuideInfoScreen', 'setOverlayEnabled', 'setPageInteraction', 'setProgress', 'setProgressColor', 'setSelectedOption', 'setSelectedTemplate', 'setSelectedTemplateTour', 'setTooltipCount', and 'setTourDataOnEdit'. Either include them or remove the dependency array.", ["2818"], "React Hook useEffect has missing dependencies: 'SetGuideName', 'currentGuideId', 'currentStep', 'getGuideTypeValue', 'handlecreatefromScratchclick', 'selectedTemplate', 'setIsGuideInfoScreen', 'setSelectedTemplate', and 'steps'. Either include them or remove the dependency array.", ["2819"], "React Hook useEffect has missing dependencies: 'setBannerPopup', 'setCurrentGuideId', 'setIsAnnouncementCreationBuilderOpen', 'setIsBannerCreationBuilderOpen', 'setIsGuideInfoScreen', 'setIsHomeScreen', 'setIsHotspotCreationBuilderOpen', 'setIsTemplateScreen', 'setIsTooltipCreationBuilderOpen', and 'setIsTooltipPopup'. Either include them or remove the dependency array.", ["2820"], "React Hook useEffect has an unnecessary dependency: 'updatedGuideData'. Either exclude it or remove the dependency array. Outer scope values like 'updatedGuideData' aren't valid dependencies because mutating them doesn't re-render the component.", ["2821"], "'getAccountIdForUpdate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'deleteClicked', 'handleStepChange', and 'updateStepClicked'. Either include them or remove the dependency array.", ["2822"], "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2823", "2824"], "'selectedStepTitle' is assigned a value but never used.", "'UserManager' is defined but never used.", "'useNavigate' is defined but never used.", "'useLocation' is defined but never used.", "'redirectPath' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loggedOut'. Either include it or remove the dependency array.", ["2825"], "'signIn' is assigned a value but never used.", "'CelebrationOutlinedIcon' is defined but never used.", "'ErrorOutlineOutlinedIcon' is defined but never used.", "'Button' is defined but never used.", "'Routes' is defined but never used.", "'RouteSharp' is defined but never used.", "'extractStateForHistory' is defined but never used.", "no-dupe-keys", "Duplicate key 'hotspotXaxis'.", "ObjectExpression", "Duplicate key 'setHotspotXaxis'.", "Duplicate key 'setSelectedTemplate'.", "Duplicate key 'toolTipGuideMetaData'.", "'isTourBanner' is assigned a value but never used.", "Duplicate key 'announcementGuideMetaData'.", "'opt' is assigned a value but never used.", "'targetStep' is assigned a value but never used.", "'future' is assigned a value but never used.", "'FALSE' is defined but never used.", "'TSectionType' is defined but never used.", "'RadioGroup' is defined but never used.", "'Radio' is defined but never used.", "'FormControlLabel' is defined but never used.", "'Input' is defined but never used.", "'Autocomplete' is defined but never used.", "'CircularProgress' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogActions' is defined but never used.", "'GifBox' is defined but never used.", "'Opacity' is defined but never used.", "'WarningIcon' is defined but never used.", "'color' is defined but never used.", "'dismissData' is assigned a value but never used.", "'setSelectActions' is assigned a value but never used.", "'setSelectedInteraction' is assigned a value but never used.", "'openInteractionList' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'currentStepIndex' is assigned a value but never used.", "'setProgress' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'updateTooltipButtonAction' is assigned a value but never used.", "'updateTooltipButtonInteraction' is assigned a value but never used.", "'selectedTemplateTour' is assigned a value but never used.", "'setProgressColor' is assigned a value but never used.", "'createWithAI' is assigned a value but never used.", "'action' is assigned a value but never used.", "'designPopup' is assigned a value but never used.", "'buttonId' is assigned a value but never used.", "'setButtonId' is assigned a value but never used.", "'cuntainerId' is assigned a value but never used.", "'setCuntainerId' is assigned a value but never used.", "'btnname' is assigned a value but never used.", "'handleCloseInteraction' is assigned a value but never used.", "'handleOpenInteraction' is assigned a value but never used.", "'sideAddButtonStyle' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo', 'setBtnName', and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2826"], "'selectedButton' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setTargetURL', and 'targetURL'. Either include them or remove the dependency array.", ["2827"], "React Hook useEffect has missing dependencies: 'selectedActions.value' and 'targetURL'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setSelectedActions' needs the current value of 'selectedActions.value'.", ["2828"], "'Box' is defined but never used.", "'useContext' is defined but never used.", "'Typography' is defined but never used.", "'AuthProvider' is defined but never used.", "'useAuth' is defined but never used.", "'AccountContext' is defined but never used.", "'clearAccessToken' is assigned a value but never used.", "'userLocalData' is assigned a value but never used.", "'SAinitialsData' is assigned a value but never used.", "'userDetails' is defined but never used.", "'ai' is defined but never used.", "'EnableAIButton' is defined but never used.", "'IsOpenAIKeyEnabledForAccount' is defined but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'setSelectedTemplateTour' is assigned a value but never used.", "'steps' is assigned a value but never used.", "'setTooltipCount' is assigned a value but never used.", "'SetGuideName' is assigned a value but never used.", "'setIsTooltipPopup' is assigned a value but never used.", "'setBannerPopup' is assigned a value but never used.", "'setElementSelected' is assigned a value but never used.", "'TooltipGuideDetails' is assigned a value but never used.", "'HotspotGuideDetailsNew' is assigned a value but never used.", "'setSelectedStepTypeHotspot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isExtensionClosed' and 'setIsExtensionClosed'. Either include them or remove the dependency array.", ["2829"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setHasAnnouncementOpened'. Either include them or remove the dependency array.", ["2830"], "React Hook useEffect has missing dependencies: 'setDrawerActiveMenu', 'setDrawerSearchText', and 'setIsPopupOpen'. Either include them or remove the dependency array. If 'setIsPopupOpen' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2831"], "'handleEnableAI' is assigned a value but never used.", "'addPersistentHighlight' is assigned a value but never used.", "'showClickFeedback' is assigned a value but never used.", "'response' is assigned a value but never used.", "'errorMessage' is assigned a value but never used.", "'axios' is defined but never used.", "'AnyMxRecord' is defined but never used.", "'useDrawerStore' is defined but never used.", "'ChecklistPopup' is defined but never used.", "'closeicon' is defined but never used.", "'closepluginicon' is defined but never used.", "'setShowLauncherSettings' is assigned a value but never used.", "'showLauncherSettings' is assigned a value but never used.", "'setIcons' is assigned a value but never used.", "'checklistColor' is assigned a value but never used.", "'GetGudeDetailsByGuideId' is defined but never used.", "'initialCompletedStatus' is assigned a value but never used.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 143) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "The 'checkpointslistData' logical expression could make the dependencies of useEffect Hook (at line 207) change on every render. To fix this, wrap the initialization of 'checkpointslistData' in its own useMemo() Hook.", "'checklistItems' is assigned a value but never used.", "'setChecklistItems' is assigned a value but never used.", "no-lone-blocks", "Nested block is redundant.", "BlockStatement", "redundantNestedBlock", "React Hook useEffect has a missing dependency: 'checkpointslistData'. Either include it or remove the dependency array.", ["2832"], "'iconColor' is assigned a value but never used.", "'base64IconFinal' is assigned a value but never used.", "'handleNavigate' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setAnchorEl' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'scrollPercentage' is assigned a value but never used.", "'setScrollPercentage' is assigned a value but never used.", "'UndoIcon' is defined but never used.", "'RedoIcon' is defined but never used.", "'canUndoValue' is assigned a value but never used.", "'canRedoValue' is assigned a value but never used.", "'Grid' is defined but never used.", "'Container' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'Alert' is defined but never used.", "'Chip' is defined but never used.", "'ViewModuleIcon' is defined but never used.", "'CodeIcon' is defined but never used.", "'TouchAppSharp' is defined but never used.", "'reselectElement' is assigned a value but never used.", "'setReselectElement' is assigned a value but never used.", "'goToNextElement' is assigned a value but never used.", "'setGoToNextElement' is assigned a value but never used.", "'setCurrentGuideId' is assigned a value but never used.", "'getCurrentGuideId' is assigned a value but never used.", "'padding' is assigned a value but never used.", "'setPadding' is assigned a value but never used.", "'setBorderColor' is assigned a value but never used.", "'borderColor' is assigned a value but never used.", "'setBackgroundColor' is assigned a value but never used.", "'overlayEnabled' is assigned a value but never used.", "'setZiindex' is assigned a value but never used.", "'setguidesSettingspopup' is assigned a value but never used.", "'setTooltipBackgroundcolor' is assigned a value but never used.", "'setTooltipBordercolor' is assigned a value but never used.", "'setTooltipBordersize' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE' is assigned a value but never used.", "'savedGuideData' is assigned a value but never used.", "'ButtonsDropdown' is assigned a value but never used.", "'setButtonsDropdown' is assigned a value but never used.", "'elementSelected' is assigned a value but never used.", "'elementbuttonClick' is assigned a value but never used.", "'highlightedButton' is assigned a value but never used.", "'mapButtonSection' is assigned a value but never used.", "'progress' is assigned a value but never used.", "'setSelectedOption' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'setHotspotPopup', 'setShowLauncherSettings', 'setShowTooltipCanvasSettings', and 'setTitlePopup'. Either include them or remove the dependency array.", ["2833"], "'toggleReselectElement' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'SetElementButtonClick', 'createWithAI', 'currentGuideId', 'interactionData', 'setButtonClick', 'setDropdownValue', 'setElementButtonName', 'setElementClick', and 'setbtnidss'. Either include them or remove the dependency array.", ["2834"], "'existingHotspot' is assigned a value but never used.", "'existingTooltip' is assigned a value but never used.", "'toggleCustomCSS' is assigned a value but never used.", "'toggleAnimation' is assigned a value but never used.", "'handleDismissDataChange' is assigned a value but never used.", "'setTooltipXaxis' is defined but never used.", "'setTooltipYaxis' is defined but never used.", "'setTooltipPosition' is defined but never used.", "'setTooltipBorderradius' is defined but never used.", "'setTooltipPadding' is defined but never used.", "'setTooltipWidth' is defined but never used.", "'updateCanvasInTooltip' is defined but never used.", "'setElementSelected' is defined but never used.", "'TextFormat' is defined but never used.", "'BUTTON_CONT_DEF_VALUE' is defined but never used.", "'saveGuide' is defined but never used.", "'setSectionColor' is assigned a value but never used.", "'setButtonProperty' is assigned a value but never used.", "'BborderSize' is assigned a value but never used.", "'Bbordercolor' is assigned a value but never used.", "'backgroundC' is assigned a value but never used.", "'setPreview' is assigned a value but never used.", "'clearGuideDetails' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'bannerButtonSelected', 'buttonColor', 'rtesContainer', 'setButtonColor', 'textAreas', and 'textBoxRef'. Either include them or remove the dependency array.", ["2835"], "React Hook useEffect has missing dependencies: 'buttonColor', 'removeTextArea', 'setButtonColor', and 'textAreas'. Either include them or remove the dependency array.", ["2836"], "React Hook useEffect has a missing dependency: 'setTextArray'. Either include it or remove the dependency array.", ["2837"], "React Hook useEffect has a missing dependency: 'textAreas'. Either include it or remove the dependency array.", ["2838"], "'setShowEmojiPicker' is assigned a value but never used.", "'enableProgress' is assigned a value but never used.", "'CustomIconButton' is defined but never used.", "'ArrowBackIosNewOutlinedIcon' is defined but never used.", "'parse' is defined but never used.", "'domToReact' is defined but never used.", "'Element' is defined but never used.", "'IconButtonSX' is defined but never used.", "'setShowBanner' is assigned a value but never used.", "'setImageSrc' is assigned a value but never used.", "'htmlContent' is assigned a value but never used.", "'Teext' is assigned a value but never used.", "'IconColor' is assigned a value but never used.", "'IconOpacity' is assigned a value but never used.", "'Width' is assigned a value but never used.", "'Radius' is assigned a value but never used.", "'Design' is assigned a value but never used.", "'brCount' is assigned a value but never used.", "'BannerEndUser' is defined but never used.", "'BannerStepPreview' is defined but never used.", "'setBannerPreview' is assigned a value but never used.", "'bannerPreview' is assigned a value but never used.", "'announcementPreview' is assigned a value but never used.", "'setAnnouncementPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'OverlayValue'. Either include it or remove the dependency array. If 'setOverlayValue' needs the current value of 'OverlayValue', you can also switch to useReducer instead of useState and read 'OverlayValue' in the reducer.", ["2839"], "'imageStyle' is assigned a value but never used.", "'dissmissIconColor' is assigned a value but never used.", "'ActionButtonBackgroundcolor' is assigned a value but never used.", "'overlay' is assigned a value but never used.", "'openInNewTab' is assigned a value but never used.", "'HotspotGuideProps' is defined but never used.", "'hotspotGuideMetaData' is assigned a value but never used.", "valid-typeof", "Invalid typeof comparison value.", "invalidV<PERSON>ue", ["2840"], "React Hook useEffect has a missing dependency: 'getElementPosition'. Either include it or remove the dependency array.", ["2841"], "React Hook useEffect has a missing dependency: 'xpath'. Either include it or remove the dependency array.", ["2842"], "React Hook useEffect has a missing dependency: 'calculateOptimalWidth'. Either include it or remove the dependency array.", ["2843"], "React Hook useEffect has a missing dependency: 'guideStep'. Either include it or remove the dependency array.", ["2844"], "'hotspotData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'savedGuideData?.GuideStep', 'selectedTemplateTour', and 'setOpenTooltip'. Either include them or remove the dependency array.", ["2845"], ["2846"], "Assignments to the 'hotspot' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "'useEffect' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'count' is assigned a value but never used.", "'selectedStepStyle' is assigned a value but never used.", "'isSelected' is assigned a value but never used.", "'isHovered' is assigned a value but never used.", "'useRef' is defined but never used.", "'ClickAwayListener' is defined but never used.", "'LinearProgress' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'updateCacheWithNewRows' is defined but never used.", "'toolTipGuideMetaData' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'tooltip' is assigned a value but never used.", "'guideName' is assigned a value but never used.", "'borderRadius' is assigned a value but never used.", "'width' is assigned a value but never used.", "'tooltipXaxis' is assigned a value but never used.", "'tooltipYaxis' is assigned a value but never used.", "'tooltipWidth' is assigned a value but never used.", "'setTooltipWidth' is assigned a value but never used.", "'setTooltipPadding' is assigned a value but never used.", "'setTooltipBorderradius' is assigned a value but never used.", "'tooltipbordersize' is assigned a value but never used.", "'tooltipPosition' is assigned a value but never used.", "'setTooltipPosition' is assigned a value but never used.", "'selectedOption' is assigned a value but never used.", "'setCurrentStepIndex' is assigned a value but never used.", "'HotspotSettings' is assigned a value but never used.", "'currentGuideId' is assigned a value but never used.", "'hoveredElement' is assigned a value but never used.", "'setHoveredElement' is assigned a value but never used.", "'overlayPosition' is assigned a value but never used.", "'setOverlayPosition' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentElement'.", "ArrowFunctionExpression", "unsafeRefs", "'removeAppliedStyleOfEle' is assigned a value but never used.", "'isElementHover' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'applyHotspotProperties', 'createWithAI', 'isCollapsed', 'isGuideInfoScreen', 'isTooltipNameScreenOpen', 'rectData', 'selectedTemplate', 'selectedTemplateTour', 'setAxisData', 'setCurrentHoveredElement', 'setElementSelected', 'setOpenTooltip', 'setTooltip', 'setXpathToTooltipMetaData', and 'syncAITooltipContainerData'. Either include them or remove the dependency array.", ["2847"], "React Hook useEffect has missing dependencies: 'isALTKeywordEnabled', 'selectedTemplate', 'selectedTemplateTour', and 'setIsALTKeywordEnabled'. Either include them or remove the dependency array.", ["2848"], "'normalizePx' is assigned a value but never used.", "'DotsStepper' is assigned a value but never used.", "'useState' is defined but never used.", "'ForkLeft' is defined but never used.", "'handleStepTypeChange' is assigned a value but never used.", "'Popover' is defined but never used.", "'CloseIcon' is defined but never used.", "'PopoverOrigin' is defined but never used.", "React Hook useEffect has missing dependencies: 'initializeTourHotspotMetadata', 'savedGuideData?.GuideStep', 'setAnnouncementPreview', 'setBannerPreview', 'setHotspotPreview', 'setOpenTooltip', and 'setTooltipPreview'. Either include them or remove the dependency array.", ["2849"], "'setCurrentUrl' is assigned a value but never used.", "Assignments to the 'savedGuideData' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'userApiService' is defined but never used.", "'AnySoaRecord' is defined but never used.", "'userUrl' is assigned a value but never used.", "'AxiosResponse' is defined but never used.", "'adminApiService' is defined but never used.", "'idsApiService' is defined but never used.", "'ArrowBackIosIcon' is defined but never used.", "'isUnSavedChanges' is assigned a value but never used.", "'openWarning' is assigned a value but never used.", "'setName' is assigned a value but never used.", "'handleKeyDown' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnnouncements'. Either include it or remove the dependency array.", ["2850"], ["2851"], "'snackbarKey' is assigned a value but never used.", "'openSnackbar' is assigned a value but never used.", "'setImageLink' is assigned a value but never used.", "'handleLinkSubmit' is assigned a value but never used.", "'TextField' is defined but never used.", "'selectedtemp' is defined but never used.", "React Hook useMemo has missing dependencies: 'handlePaste', 'selectedTemplate', and 'selectedTemplateTour'. Either include them or remove the dependency array.", ["2852"], "'backgroundcoloricon' is defined but never used.", "'ButtonSettings' is defined but never used.", "'buttonProperty' is assigned a value but never used.", "'isEditingPrevious' is assigned a value but never used.", "'isEditingContinue' is assigned a value but never used.", "'previousButtonText' is assigned a value but never used.", "'continueButtonText' is assigned a value but never used.", "'buttonText' is assigned a value but never used.", "'setButtonText' is assigned a value but never used.", "'buttonToEdit' is assigned a value but never used.", "'isDeleteIcon' is assigned a value but never used.", "'isEditingButton' is assigned a value but never used.", "'isEditing' is assigned a value but never used.", "'setIsEditing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setButtonProperty'. Either include it or remove the dependency array.", ["2853"], "'handlePreviousTextChange' is assigned a value but never used.", "'handleContinueTextChange' is assigned a value but never used.", "'toggleEdit' is assigned a value but never used.", "'handlePreviousBlur' is assigned a value but never used.", "'handleContinueBlur' is assigned a value but never used.", "'handleChangeButton' is assigned a value but never used.", "'handleEditButtonText' is assigned a value but never used.", "'LauncherSettings' is defined but never used.", "React Hook useEffect has missing dependencies: 'checkpointslistData' and 'completedStatus'. Either include them or remove the dependency array.", ["2854"], "React Hook useEffect has missing dependencies: 'createWithAI' and 'interactionData'. Either include them or remove the dependency array.", ["2855"], ["2856"], "React Hook useEffect has a missing dependency: 'checklistGuideMetaData'. Either include it or remove the dependency array.", ["2857"], "'toggleItemCompletion' is assigned a value but never used.", "'beta' is defined but never used.", "'setIsCollapsed' is assigned a value but never used.", "'accountId' is assigned a value but never used.", "'handleClick' is assigned a value but never used.", "'micicon' is defined but never used.", "'micicon_hover' is defined but never used.", "'PerfectScrollbar' is defined but never used.", "'isChatOpen' is assigned a value but never used.", "'setIsChatOpen' is assigned a value but never used.", "'isMicHovered' is assigned a value but never used.", "'setIsMicHovered' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'accountId' and 'openSnackbar'. Either include them or remove the dependency array.", ["2858"], "'handleSpeechRecognition' is assigned a value but never used.", "'isTourCreationPrompt' is assigned a value but never used.", "'parseTourSteps' is assigned a value but never used.", "'dataNew' is assigned a value but never used.", "'stepDataNew' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setElementSelected'. Either include it or remove the dependency array.", ["2859"], "'useReducer' is defined but never used.", "'SelectChangeEvent' is defined but never used.", "'Switch' is defined but never used.", "'ToggleButton' is defined but never used.", "'ToggleButtonGroup' is defined but never used.", "'BUTTON_CONT_DEF_VALUE_1' is defined but never used.", "'CANVAS_DEFAULT_VALUE' is defined but never used.", "'IMG_CONT_DEF_VALUE' is defined but never used.", "'HOTSPOT_DEFAULT_VALUE' is defined but never used.", "'InfoFilled' is defined but never used.", "'QuestionFill' is defined but never used.", "'Reselect' is defined but never used.", "'Solid' is defined but never used.", "'AddCircleOutlineIcon' is defined but never used.", "'InsertPhotoIcon' is defined but never used.", "'PersonIcon' is defined but never used.", "'FavoriteIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'ErrorOutlineIcon' is defined but never used.", "'position' is defined but never used.", "'titlePopup' is assigned a value but never used.", "'setTitlePopup' is assigned a value but never used.", "'titleColor' is assigned a value but never used.", "'launcherColor' is assigned a value but never used.", "'hasChanges' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistGuideMetaData', 'checklistLauncherProperties', and 'icons'. Either include them or remove the dependency array.", ["2860"], "'handleTitleColorChange' is assigned a value but never used.", "'handledesignclose' is assigned a value but never used.", "'handleSizeChange' is assigned a value but never used.", "'onReselectElement' is assigned a value but never used.", "'handleIconColorChange' is assigned a value but never used.", "'handleLauncherColorChange' is assigned a value but never used.", "'type' is assigned a value but never used.", "'text' is assigned a value but never used.", "'setText' is assigned a value but never used.", "'textColor' is assigned a value but never used.", "'setTextColor' is assigned a value but never used.", "'icon' is assigned a value but never used.", "'appliedIconColorBase64Icon' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistLauncherProperties', 'icons', and 'updateChecklistLauncher'. Either include them or remove the dependency array.", ["2861"], "'setPositionLeft' is assigned a value but never used.", "'setSetPositionLeft' is assigned a value but never used.", "'deleteicon' is defined but never used.", "'deletestep' is defined but never used.", "'editpricol' is defined but never used.", "'getAllGuides' is defined but never used.", "'ShowLauncherSettings' is assigned a value but never used.", "'setTitleColor' is assigned a value but never used.", "'checkpointsPopup' is assigned a value but never used.", "'checkpointTitleColor' is assigned a value but never used.", "'setCheckpointTitleColor' is assigned a value but never used.", "'checkpointTitleDescription' is assigned a value but never used.", "'setCheckpointTitleDescription' is assigned a value but never used.", "'checkpointIconColor' is assigned a value but never used.", "'setCheckpointIconColor' is assigned a value but never used.", "'setUnlockCheckPointInOrder' is assigned a value but never used.", "'unlockCheckPointInOrder' is assigned a value but never used.", "'checkPointMessage' is assigned a value but never used.", "'setCheckPointMessage' is assigned a value but never used.", ["2862"], "'interactions' is assigned a value but never used.", "'setInteractions' is assigned a value but never used.", "'skip' is assigned a value but never used.", "'setSkip' is assigned a value but never used.", "'top' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'dropdownRef' is assigned a value but never used.", "'RadioButtonUncheckedIcon' is defined but never used.", "'RadioButtonCheckedIcon' is defined but never used.", "'topCenter' is defined but never used.", "'OverlaySettingsProps' is defined but never used.", "'ElementsSettingsProps' is defined but never used.", "'setTooltipElementOptions' is assigned a value but never used.", "'updateprogressclick' is assigned a value but never used.", "'displayType' is assigned a value but never used.", "'dontShowAgain' is assigned a value but never used.", "'colors' is assigned a value but never used.", "'handleDisplayTypeChange' is assigned a value but never used.", "'handleBorderColorChange' is assigned a value but never used.", "'handleDontShowAgainChange' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dismissData.Color' and 'setDismiss'. Either include them or remove the dependency array.", ["2863"], "'defaultDots' is defined but never used.", "'topLeft' is defined but never used.", "'topRight' is defined but never used.", "'middleLeft' is defined but never used.", "'middleCenter' is defined but never used.", "'middleRight' is defined but never used.", "'bottomLeft' is defined but never used.", "'bottomMiddle' is defined but never used.", "'bottomRight' is defined but never used.", "'topcenter' is defined but never used.", "'setCanvasSetting' is assigned a value but never used.", "'announcement<PERSON>son' is assigned a value but never used.", "'setWidth' is assigned a value but never used.", "'setBorderRadius' is assigned a value but never used.", "'Annpadding' is assigned a value but never used.", "'setAnnPadding' is assigned a value but never used.", "'AnnborderSize' is assigned a value but never used.", "'setAnnBorderSize' is assigned a value but never used.", "'Bposition' is assigned a value but never used.", "'setBposition' is assigned a value but never used.", "'handleBackgroundColorChange' is assigned a value but never used.", "'checklistTitle' is assigned a value but never used.", "'setChecklistTitle' is assigned a value but never used.", "'checklistSubTitle' is assigned a value but never used.", "'setChecklistSubTitle' is assigned a value but never used.", "'setTempTitle' is assigned a value but never used.", "'settempTempTitle' is assigned a value but never used.", "'handleBlur' is assigned a value but never used.", "'setZindeex' is assigned a value but never used.", "'setOverlayEnabled' is assigned a value but never used.", "'handlePositionChange' is assigned a value but never used.", "'tempBorderSize' is assigned a value but never used.", "'setTempBorderSize' is assigned a value but never used.", "'tempZIndex' is assigned a value but never used.", "'setTempZIndex' is assigned a value but never used.", "'tempBorderColor' is assigned a value but never used.", "'setTempBorderColor' is assigned a value but never used.", "'tempBackgroundColor' is assigned a value but never used.", "'setTempBackgroundColor' is assigned a value but never used.", "'tempSectionColor' is assigned a value but never used.", "'setTempSectionColor' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogContent' is defined but never used.", "'useMediaQuery' is defined but never used.", "'useTheme' is defined but never used.", "'zIndex' is defined but never used.", "'buttonsContainer' is assigned a value but never used.", "'cloneButtonContainer' is assigned a value but never used.", "'addNewButton' is assigned a value but never used.", "'deleteButton' is assigned a value but never used.", "'deleteButtonContainer' is assigned a value but never used.", "'updateContainer' is assigned a value but never used.", "'updateButtonInteraction' is assigned a value but never used.", "'setBtnBgColor' is assigned a value but never used.", "'setBtnTextColor' is assigned a value but never used.", "'setBtnBorderColor' is assigned a value but never used.", "'setBtnName' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'selectedPosition' is assigned a value but never used.", "'url' is assigned a value but never used.", "'setUrl' is assigned a value but never used.", "'setAction' is assigned a value but never used.", "'setOpenInNewTab' is assigned a value but never used.", "'setColors' is assigned a value but never used.", "'buttonNameError' is assigned a value but never used.", "'setButtonNameError' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'defaultButtonColors.backgroundColor', 'defaultButtonColors.borderColor', 'defaultButtonColors.color', 'getCurrentButtonInfo', 'setCurrentButtonName', 'setSelectedTab', and 'setTargetURL'. Either include them or remove the dependency array.", ["2864"], "'positions' is assigned a value but never used.", "'curronButtonInfo' is assigned a value but never used.", "React Hook useMemo has missing dependencies: 'getCurrentButtonInfo' and 'setCurrentButtonName'. Either include them or remove the dependency array.", ["2865"], "'handlePositionClick' is assigned a value but never used.", "'imageContainerStyle' is assigned a value but never used.", "'iconRowStyle' is assigned a value but never used.", "'iconTextStyle' is assigned a value but never used.", "'setOpenTooltip' is assigned a value but never used.", "'setTooltipPositionByXpath' is assigned a value but never used.", "'updateTooltipBtnContainer' is assigned a value but never used.", "'updateTooltipImageContainer' is assigned a value but never used.", "'RefObject' is defined but never used.", "'CustomWidthTooltip' is defined but never used.", "'EXTENSION_PART' is defined but never used.", "'TOOLTIP_HEIGHT' is defined but never used.", "'TOOLTIP_MN_WIDTH' is defined but never used.", "'TOOLTIP_MX_WIDTH' is defined but never used.", "'Code' is defined but never used.", "'VideoLibrary' is defined but never used.", "'RTE' is defined but never used.", "'translate' is assigned a value but never used.", "'tooltipBackgroundcolor' is assigned a value but never used.", "'tooltipborderradius' is assigned a value but never used.", "'tooltipBordercolor' is assigned a value but never used.", "'tooltippadding' is assigned a value but never used.", "'elementClick' is assigned a value but never used.", "'setDismiss' is assigned a value but never used.", "'savedRange' is assigned a value but never used.", "'setSaveRange' is assigned a value but never used.", "'isEditorFocused' is assigned a value but never used.", "'setIsEditorFocused' is assigned a value but never used.", "'handleDeleteSection' is assigned a value but never used.", "'handleCloneContainer' is assigned a value but never used.", "'handleDragStart' is assigned a value but never used.", "'handleDragEnter' is assigned a value but never used.", "'handleDragEnd' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'popupPosition', 'setCurrentHoveredElement', 'setTooltipPositionByXpath', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2866"], "'canvasProperties' is assigned a value but never used.", "'RTEToolbar' is assigned a value but never used.", "React Hook useMemo has a missing dependency: 'handleFocus'. Either include it or remove the dependency array.", ["2867"], "'isInsideJoditPopup' is assigned a value but never used.", "'isPasteEvent' is assigned a value but never used.", "'CustomImage' is defined but never used.", "'pageinteraction' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'smoothScrollTo'. Either exclude it or remove the dependency array.", ["2868"], "React Hook useEffect has missing dependencies: 'currentStep' and 'selectedTemplate'. Either include them or remove the dependency array.", ["2869"], "React Hook useEffect has missing dependencies: 'currentStep' and 'currentStepIndex'. Either include them or remove the dependency array.", ["2870"], "React Hook useCallback has unnecessary dependencies: 'calculateBestPosition' and 'scrollToTargetElement'. Either exclude them or remove the dependency array.", ["2871"], "React Hook useCallback has a missing dependency: 'steps'. Either include it or remove the dependency array.", ["2872"], "React Hook useEffect has a missing dependency: 'currentStepIndex'. Either include it or remove the dependency array.", ["2873"], "React Hook useEffect has a missing dependency: 'updateTargetAndPosition'. Either include it or remove the dependency array.", ["2874"], ["2875"], ["2876"], "'hasOnlyTextContent' is assigned a value but never used.", "'hasOnlyButton' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is defined but never used.", "'TCanvas' is defined but never used.", "'updateDesignelementInTooltip' is assigned a value but never used.", "'CANVAS_DEFAULT_VALUE_HOTSPOT' is assigned a value but never used.", "'dismiss' is assigned a value but never used.", "'setSelectedPosition' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentStep', 'selectedTemplate', 'selectedTemplateTour', 'setTooltipBackgroundcolor', 'setTooltipBordercolor', 'setTooltipBorderradius', 'setTooltipBordersize', 'setTooltipPadding', 'setTooltipPosition', 'setTooltipWidth', 'setTooltipXaxis', 'setTooltipYaxis', and 'toolTipGuideMetaData'. Either include them or remove the dependency array.", ["2877"], "'guideStatus' is assigned a value but never used.", "'RemoveIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'DriveFolderUploadIcon' is defined but never used.", "'BackupIcon' is defined but never used.", "'modifySVGColor' is assigned a value but never used.", "'setCheckPointsPopup' is assigned a value but never used.", "'handleCheckPointIconColorChange' is assigned a value but never used.", "'handleCheckPointTitleColorChange' is assigned a value but never used.", "'handleCheckPointDescriptionColorChange' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["2878"], "'handleMenuScroll' is assigned a value but never used.", "'useCallback' is defined but never used.", "'checkpointsEditPopup' is assigned a value but never used.", "'updateChecklistCheckPoints' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filteredInteractions'. Either include it or remove the dependency array.", ["2879"], "'applyclicked' is assigned a value but never used.", "'isSearching' is assigned a value but never used.", ["2880"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checklistCheckpointListProperties' and 'icons'. Either include them or remove the dependency array.", ["2881"], "'handleColorChange' is assigned a value but never used.", "'FolderIcon' is defined but never used.", "'useAsyncError' is defined but never used.", "'getCurrentButtonInfo' is assigned a value but never used.", "'clickTimeout' is defined but never used.", "'handleEditButtonName' is assigned a value but never used.", "'Modal' is defined but never used.", "'IMG_EXPONENT' is defined but never used.", "'getAllFiles' is defined but never used.", "'selectedColor' is assigned a value but never used.", "'formOfUpload' is assigned a value but never used.", "'setFormOfUpload' is assigned a value but never used.", "'urll' is defined but never used.", "'handleHyperlinkClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setImageAnchorEl'. Either include it or remove the dependency array.", ["2882"], "'Image' is defined but never used.", "'Link' is defined but never used.", "'setIsUnSavedChanges' is assigned a value but never used.", "'setHtmlContent' is assigned a value but never used.", "'setTextvaluess' is assigned a value but never used.", "'setBackgroundC' is assigned a value but never used.", "'bpadding' is assigned a value but never used.", "'handleTooltipRTEBlur' is assigned a value but never used.", "'ColorResult' is defined but never used.", "'setBtnIdss' is assigned a value but never used.", "'gotoNextButtonId' is assigned a value but never used.", "'matchingButton' is assigned a value but never used.", ["2883"], "'getSavedLanguage' is assigned a value but never used.", "'saveLanguage' is assigned a value but never used.", "'clearSavedLanguage' is assigned a value but never used.", "'getAvailableLanguages' is defined but never used.", "React Hook useEffect has a missing dependency: 'userInfoObj'. Either include it or remove the dependency array.", ["2884"], "'orgId' is assigned a value but never used.", {"desc": "2885", "fix": "2886"}, {"desc": "2887", "fix": "2888"}, {"desc": "2889", "fix": "2890"}, {"desc": "2891", "fix": "2892"}, {"desc": "2893", "fix": "2894"}, {"desc": "2895", "fix": "2896"}, {"desc": "2897", "fix": "2898"}, {"desc": "2899", "fix": "2900"}, {"desc": "2901", "fix": "2902"}, {"desc": "2903", "fix": "2904"}, {"desc": "2905", "fix": "2906"}, {"desc": "2907", "fix": "2908"}, {"desc": "2909", "fix": "2910"}, {"desc": "2911", "fix": "2912"}, {"desc": "2913", "fix": "2914"}, {"desc": "2915", "fix": "2916"}, {"desc": "2917", "fix": "2918"}, {"desc": "2919", "fix": "2920"}, {"messageId": "2921", "fix": "2922", "desc": "2923"}, {"messageId": "2924", "fix": "2925", "desc": "2926"}, {"desc": "2927", "fix": "2928"}, {"desc": "2929", "fix": "2930"}, {"desc": "2931", "fix": "2932"}, {"desc": "2933", "fix": "2934"}, {"desc": "2935", "fix": "2936"}, {"desc": "2937", "fix": "2938"}, {"desc": "2939", "fix": "2940"}, {"desc": "2941", "fix": "2942"}, {"desc": "2943", "fix": "2944"}, {"desc": "2945", "fix": "2946"}, {"desc": "2947", "fix": "2948"}, {"desc": "2949", "fix": "2950"}, {"desc": "2951", "fix": "2952"}, {"desc": "2953", "fix": "2954"}, {"desc": "2955", "fix": "2956"}, {"messageId": "2957", "data": "2958", "fix": "2959", "desc": "2960"}, {"desc": "2961", "fix": "2962"}, {"desc": "2963", "fix": "2964"}, {"desc": "2965", "fix": "2966"}, {"desc": "2967", "fix": "2968"}, {"desc": "2969", "fix": "2970"}, {"desc": "2971", "fix": "2972"}, {"desc": "2973", "fix": "2974"}, {"desc": "2975", "fix": "2976"}, {"desc": "2977", "fix": "2978"}, {"desc": "2979", "fix": "2980"}, {"desc": "2981", "fix": "2982"}, {"desc": "2983", "fix": "2984"}, {"desc": "2985", "fix": "2986"}, {"desc": "2987", "fix": "2988"}, {"desc": "2989", "fix": "2990"}, {"desc": "2941", "fix": "2991"}, {"desc": "2992", "fix": "2993"}, {"desc": "2994", "fix": "2995"}, {"desc": "2996", "fix": "2997"}, {"desc": "2998", "fix": "2999"}, {"desc": "3000", "fix": "3001"}, {"desc": "2992", "fix": "3002"}, {"desc": "3003", "fix": "3004"}, {"desc": "3005", "fix": "3006"}, {"desc": "3007", "fix": "3008"}, {"desc": "3009", "fix": "3010"}, {"desc": "3011", "fix": "3012"}, {"desc": "3013", "fix": "3014"}, {"desc": "3015", "fix": "3016"}, {"desc": "3017", "fix": "3018"}, {"desc": "3019", "fix": "3020"}, {"desc": "3021", "fix": "3022"}, {"desc": "3023", "fix": "3024"}, {"desc": "3025", "fix": "3026"}, {"desc": "3025", "fix": "3027"}, {"desc": "3028", "fix": "3029"}, {"desc": "3030", "fix": "3031"}, {"desc": "3032", "fix": "3033"}, {"desc": "3034", "fix": "3035"}, {"desc": "3032", "fix": "3036"}, {"desc": "3037", "fix": "3038"}, {"desc": "3039", "fix": "3040"}, {"desc": "3041", "fix": "3042"}, {"desc": "3043", "fix": "3044"}, "Update the dependencies array to be: []", {"range": "3045", "text": "3046"}, "Update the dependencies array to be: [fetchGuideDetails, hotspot, hotspotClicked]", {"range": "3047", "text": "3048"}, "Update the dependencies array to be: [designPopup, setDesignPopup]", {"range": "3049", "text": "3050"}, "Update the dependencies array to be: [isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", {"range": "3051", "text": "3052"}, "Update the dependencies array to be: [isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", {"range": "3053", "text": "3054"}, "Update the dependencies array to be: [savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", {"range": "3055", "text": "3056"}, "Update the dependencies array to be: [currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", {"range": "3057", "text": "3058"}, "Update the dependencies array to be: [openStepDropdown, plusIconclick, setSettingAnchorEl]", {"range": "3059", "text": "3060"}, "Update the dependencies array to be: [createWithAI, setIsUnSavedChanges, stepCreation]", {"range": "3061", "text": "3062"}, "Update the dependencies array to be: [isLoggedIn, organizationId, userType]", {"range": "3063", "text": "3064"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showBannerenduser, showTooltipenduser, showHotspotenduser, isTourTemplate]", {"range": "3065", "text": "3066"}, "Update the dependencies array to be: [currentGuide?.GuideStep, currentStep]", {"range": "3067", "text": "3068"}, "Update the dependencies array to be: [cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", {"range": "3069", "text": "3070"}, "Update the dependencies array to be: [SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", {"range": "3071", "text": "3072"}, "Update the dependencies array to be: [isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", {"range": "3073", "text": "3074"}, "Update the dependencies array to be: [isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", {"range": "3075", "text": "3076"}, "Update the dependencies array to be: [pendingWebTourModal, isTourPopupOpen, tourModalSource]", {"range": "3077", "text": "3078"}, "Update the dependencies array to be: [currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", {"range": "3079", "text": "3080"}, "removeEscape", {"range": "3081", "text": "3082"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "3083", "text": "3084"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [loggedOut]", {"range": "3085", "text": "3086"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", {"range": "3087", "text": "3088"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", {"range": "3089", "text": "3090"}, "Update the dependencies array to be: [selectedActions.value, targetURL]", {"range": "3091", "text": "3092"}, "Update the dependencies array to be: [isExtensionClosed, setIsExtensionClosed]", {"range": "3093", "text": "3094"}, "Update the dependencies array to be: [hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", {"range": "3095", "text": "3096"}, "Update the dependencies array to be: [isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", {"range": "3097", "text": "3098"}, "Update the dependencies array to be: [checkpointslistData]", {"range": "3099", "text": "3100"}, "Update the dependencies array to be: [selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", {"range": "3101", "text": "3102"}, "Update the dependencies array to be: [SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", {"range": "3103", "text": "3104"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", {"range": "3105", "text": "3106"}, "Update the dependencies array to be: [bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", {"range": "3107", "text": "3108"}, "Update the dependencies array to be: [setTextArray, textAreas]", {"range": "3109", "text": "3110"}, "Update the dependencies array to be: [createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", {"range": "3111", "text": "3112"}, "Update the dependencies array to be: [OverlayValue]", {"range": "3113", "text": "3114"}, "suggestString", {"type": "3115"}, {"range": "3116", "text": "3117"}, "Use `\"undefined\"` instead of `undefined`.", "Update the dependencies array to be: [getElementPosition, xpath]", {"range": "3118", "text": "3119"}, "Update the dependencies array to be: [savedGuideData, xpath]", {"range": "3120", "text": "3121"}, "Update the dependencies array to be: [textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", {"range": "3122", "text": "3123"}, "Update the dependencies array to be: [currentStep, guideStep, setOpenTooltip]", {"range": "3124", "text": "3125"}, "Update the dependencies array to be: [currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", {"range": "3126", "text": "3127"}, "Update the dependencies array to be: [currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", {"range": "3128", "text": "3129"}, "Update the dependencies array to be: [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", {"range": "3130", "text": "3131"}, "Update the dependencies array to be: [elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", {"range": "3132", "text": "3133"}, "Update the dependencies array to be: [stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", {"range": "3134", "text": "3135"}, "Update the dependencies array to be: [paginationModel, activeTab, Open, accountId, fetchAnnouncements]", {"range": "3136", "text": "3137"}, "Update the dependencies array to be: [fetchAnnouncements, searchQuery]", {"range": "3138", "text": "3139"}, "Update the dependencies array to be: [handlePaste, isRtlDirection, selectedTemplate, selectedTemplateTour, toolbarVisibleRTEId]", {"range": "3140", "text": "3141"}, "Update the dependencies array to be: [setButtonProperty]", {"range": "3142", "text": "3143"}, "Update the dependencies array to be: [checkpointslistData, completedStatus]", {"range": "3144", "text": "3145"}, "Update the dependencies array to be: [selectedItem, activeItem, createWithAI, interactionData]", {"range": "3146", "text": "3147"}, {"range": "3148", "text": "3100"}, "Update the dependencies array to be: [checklistGuideMetaData]", {"range": "3149", "text": "3150"}, "Update the dependencies array to be: [accountId, openSnackbar]", {"range": "3151", "text": "3152"}, "Update the dependencies array to be: [setElementSelected]", {"range": "3153", "text": "3154"}, "Update the dependencies array to be: [checklistGuideMetaData, checklistLauncherProperties, icons]", {"range": "3155", "text": "3156"}, "Update the dependencies array to be: [checklistLauncherProperties, icons, updateChecklistLauncher]", {"range": "3157", "text": "3158"}, {"range": "3159", "text": "3150"}, "Update the dependencies array to be: [dismissData.Color, dismissData?.dismisssel, setDismiss]", {"range": "3160", "text": "3161"}, "Update the dependencies array to be: [settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", {"range": "3162", "text": "3163"}, "Update the dependencies array to be: [getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", {"range": "3164", "text": "3165"}, "Update the dependencies array to be: [currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", {"range": "3166", "text": "3167"}, "Update the dependencies array to be: [handleFocus, isRtlDirection]", {"range": "3168", "text": "3169"}, "Update the dependencies array to be: [universalScrollTo]", {"range": "3170", "text": "3171"}, "Update the dependencies array to be: [currentStep, currentStepIndex, interactWithPage, selectedTemplate]", {"range": "3172", "text": "3173"}, "Update the dependencies array to be: [currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", {"range": "3174", "text": "3175"}, "Update the dependencies array to be: [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", {"range": "3176", "text": "3177"}, "Update the dependencies array to be: [selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", {"range": "3178", "text": "3179"}, "Update the dependencies array to be: [currentStepData, currentStepIndex, handleNext]", {"range": "3180", "text": "3181"}, "Update the dependencies array to be: [currentStepData, currentUrl, updateTargetAndPosition]", {"range": "3182", "text": "3183"}, {"range": "3184", "text": "3183"}, "Update the dependencies array to be: [currentStepData, currentUrl, rect, updateTargetAndPosition]", {"range": "3185", "text": "3186"}, "Update the dependencies array to be: [currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", {"range": "3187", "text": "3188"}, "Update the dependencies array to be: [fetchData]", {"range": "3189", "text": "3190"}, "Update the dependencies array to be: [selectedInteraction, interactions, searchTerm, filteredInteractions]", {"range": "3191", "text": "3192"}, {"range": "3193", "text": "3190"}, "Update the dependencies array to be: [checklistCheckpointListProperties, icons]", {"range": "3194", "text": "3195"}, "Update the dependencies array to be: [setImageAnchorEl, tooltip.visible]", {"range": "3196", "text": "3197"}, "Update the dependencies array to be: [settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", {"range": "3198", "text": "3199"}, "Update the dependencies array to be: [orgId, accessToken, userInfoObj]", {"range": "3200", "text": "3201"}, [18280, 18302], "[]", [26591, 26632], "[fetchGuideDetails, hotspot, hotspotClicked]", [27114, 27127], "[designPopup, setDesignPopup]", [29851, 29998], "[isTemplateScreen, isPopupOpen, bannerPopup, currentGuideId, selectedTemplate, selectedTemplateTour, determineCurrentScreen]", [30476, 30836], "[isCollapsed, isGuideInfoScreen, isTemplateScreen, isPopupOpen, bannerPopup, currentScreen, selectedTemplate, selectedTemplateTour, guideName, currentGuideId, guideStep, htmlContent, imageSrc, buttonColor, imageName, errors]", [35444, 35478], "[savedGuideData, isSaveInProgress, currentGuideId, selectedTemplate, selectedTemplateTour, bannerPopup, setBannerPopup, setIsBannerCreationBuilderOpen, setIsAnnouncementCreationBuilderOpen, setIsTooltipCreationBuilderOpen, setIsHotspotCreationBuilderOpen, clearBannerButtonDetials, updateRTEContainerOnReload, setBannerButtonSelected, updateButtonContainerOnReload]", [70781, 70794], "[currentStep, elementSelected, handleClose, isShowIcon, resetALTKeywordForNewTooltip, setElementSelected, toolTipGuideMetaData]", [86809, 86842], "[openStepDropdown, plusIconclick, setSettingAnchorEl]", [102025, 102039], "[createWithAI, setIsUnSavedChanges, stepCreation]", [125141, 125169], "[isLoggedIn, organizationId, userType]", [131291, 131443], "[isAnnounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, showB<PERSON><PERSON><PERSON>er, showTooltipenduser, showHotspotenduser, isTourTemplate]", [161132, 161189], "[currentGuide?.GuideStep, currentStep]", [166445, 166462], "[cleanupDuplicateSteps, createWithAI, currentGuideId, elementSelected, interactionData, resetHeightofBanner]", [172395, 172428], "[SetGuideName, cleanupDuplicateSteps, createWithAI, currentGuideId, currentStep, getGuideTypeValue, handlecreatefromScratchclick, interactionData, overlayEnabled, pageinteraction, progress, resetHeightofBanner, setBannerPopup, setBposition, setDismiss, setIsGuideInfoScreen, setOverlayEnabled, setPageInteraction, setProgress, setProgressColor, setSelectedOption, setSelectedTemplate, setSelectedTemplateTour, setTooltipCount, setTourDataOnEdit]", [173138, 173239], "[isAnnouncementPopupOpen, showHotspotenduser, isHotspotPopupOpen, isTourTemplate, isChecklistPreview, currentGuideId, setIsGuideInfoScreen, setSelectedTemplate, handlecreatefromScratchclick, SetGuideName, steps, selectedTemplate, getGuideTypeValue, currentStep]", [177339, 177351], "[isLoggedIn, setBannerPopup, setCurrentGuideId, setIsAnnouncementCreationBuilderOpen, setIsBannerCreationBuilderOpen, setIsGuideInfoScreen, setIsHomeScreen, setIsHotspotCreationBuilderOpen, setIsTemplateScreen, setIsTooltipCreationBuilderOpen, setIsTooltipPopup]", [177771, 177844], "[pendingWebTourModal, isTourPopupOpen, tourModalSource]", [178413, 178420], "[currentStep, deleteClicked, handleStepChange, steps, updateStepClicked]", [199138, 199139], "", [199138, 199138], "\\", [4501, 4503], "[loggedOut]", [16077, 16132], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName, setBtnName]", [17506, 17561], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, targetURL]", [18006, 18008], "[selectedActions.value, targetURL]", [2347, 2349], "[isExtensionClosed, setIsExtensionClosed]", [3014, 3037], "[hasAnnouncementOpened, setDrawerActiveMenu, setDrawerSearchText, setHasAnnouncementOpened]", [3381, 3424], "[isExtensionClosed, activeMenu, searchText, setDrawerActiveMenu, setDrawerSearchText, setIsPopupOpen]", [5960, 5993], "[checkpointslistData]", [5310, 5350], "[selectedTemplate, selectedTemplateTour, setHotspotPopup, setShowLauncherSettings, setShowTooltipCanvasSettings, setTitlePopup]", [10234, 10269], "[SetElementButtonClick, createWithAI, currentGuideId, currentStep, interactionData, setButtonClick, setDropdownValue, setElementButtonName, setElementClick, setbtnidss, toolTipGuideMetaData]", [6153, 6155], "[bannerButtonSelected, buttonColor, rtesContainer, setButtonColor, textAreas, textBoxRef]", [7218, 7240], "[bannerButtonSelected, buttonColor, removeTextArea, setButtonColor, textAreas]", [7401, 7412], "[setTex<PERSON><PERSON><PERSON><PERSON>, text<PERSON><PERSON>s]", [8963, 9028], "[createWithAI, bannerButtonSelected, buttonColor, setButtonColor, textAreas]", [4728, 4730], "[OverlayValue]", "undefined", [6583, 6592], "\"undefined\"", [6719, 6726], "[getElementPosition, xpath]", [6855, 6871], "[savedGuideData, xpath]", [14985, 15050], "[textFieldProperties, imageProperties, customButton, currentStep, calculateOptimalWidth]", [19065, 19124], "[currentStep, guideStep, setOpenTooltip]", [19978, 20020], "[currentStep, isHotspotPopupOpen, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, toolTipGuideMetaData]", [20812, 20854], "[currentStep, savedGuideData?.GuideStep, selectedTemplateTour, setOpenTooltip, showHotspotenduser, toolTipGuideMetaData]", [22776, 22871], "[toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement, rectData, selectedTemplate, selectedTemplateTour, isTooltipNameScreenOpen, isCollapsed, setCurrentHoveredElement, isGuideInfoScreen, setElementSelected, setAxisData, setTooltip, setOpenTooltip, setXpathToTooltipMetaData, createWithAI, syncAITooltipContainerData, applyHotspotProperties]", [26739, 26756], "[elementSelected, isALTKeywordEnabled, selectedTemplate, selectedTemplateTour, setIsALTKeywordEnabled]", [6220, 6243], "[stepType, currentStep, setAnnouncementPreview, setBannerPreview, setTooltipPreview, setHotspotPreview, savedGuideData?.GuideStep, setOpenTooltip, initializeTourHotspotMetadata]", [8894, 8939], "[paginationModel, activeTab, Open, accountId, fetchAnnouncements]", [9245, 9258], "[fetchAnnouncements, searchQuery]", [15572, 15609], "[handlePaste, isRtlDirection, selectedTemplate, selectedTemplateTour, toolbarVisibleRTEId]", [2651, 2653], "[setButtonProperty]", [3543, 3545], "[checkpointslistData, completedStatus]", [4387, 4413], "[selectedItem, activeItem, createWithAI, interactionData]", [4517, 4551], [5507, 5534], "[checklistGuideMetaData]", [4604, 4606], "[accountId, openSnackbar]", [17421, 17423], "[setElementSelected]", [4872, 4874], "[checklistGuideMetaData, checklistLauncherProperties, icons]", [9650, 9652], "[checklistLauncherProperties, icons, updateChecklistLauncher]", [3211, 3251], [6878, 6903], "[dismissData.Color, dismissData?.dismisssel, setDismiss]", [4631, 4686], "[settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, setCurrentButtonName, setTargetURL, defaultButtonColors.backgroundColor, defaultButtonColors.borderColor, defaultButtonColors.color, setSelectedTab]", [6164, 6219], "[getCurrentButtonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, setCurrentButtonName]", [9922, 9935], "[currentStep, openTooltip, popupPosition, setCurrentHoveredElement, setTooltipPositionByXpath, toolTipGuideMetaData]", [26036, 26052], "[handleFocus, isRtlDirection]", [19204, 19239], "[universalScrollTo]", [20357, 20393], "[currentStep, currentStepIndex, interactWithPage, selectedTemplate]", [20804, 20848], "[currentStep, currentStepData?.overlay, currentStepIndex, interactWithPage]", [29926, 30081], "[currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, setCurrentStep, setCurrentStepIndex]", [32166, 32271], "[selectedTemplate, currentStepIndex, setCurrentStep, currentStep, steps]", [33472, 33501], "[currentStepData, currentStepIndex, handleNext]", [33978, 34007], "[currentStepData, currentUrl, updateTargetAndPosition]", [34419, 34448], [34507, 34542], "[currentStepData, currentUrl, rect, updateTargetAndPosition]", [7898, 7945], "[currentStep, selectedTemplate, selectedTemplateTour, setTooltipBackgroundcolor, setTooltipBordercolor, setTooltipBorderradius, setTooltipBordersize, setTooltipPadding, setTooltipPosition, setTooltipWidth, setTooltipXaxis, setTooltipYaxis, toolTipGuideMetaData]", [7680, 7682], "[fetchData]", [4285, 4332], "[selectedInteraction, interactions, searchTerm, filteredInteractions]", [6555, 6557], [12538, 12540], "[checklistCheckpointListProperties, icons]", [10130, 10147], "[setImageAnchorEl, tooltip.visible]", [4246, 4396], "[settingAnchorEl.value, buttonInfo, settingAnchorEl.containerId, settingAnchorEl.buttonId, getCurrentButtonInfo, btnidss, currentGuideId, currentStep, createWithAI, interactionData]", [4596, 4616], "[orgId, accessToken, userInfoObj]"]