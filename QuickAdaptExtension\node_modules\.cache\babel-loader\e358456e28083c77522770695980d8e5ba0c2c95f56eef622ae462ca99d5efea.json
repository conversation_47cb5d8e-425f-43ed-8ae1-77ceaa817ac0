{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useMemo } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const contentRef = useRef(\"\");\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor - now works with individual RTEs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return; // No RTE is currently being edited\n\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n\n      // Get the container ref for the currently editing RTE\n      const currentContainerRef = getContainerRef(editingRTEId);\n\n      // Check if the target is inside the currently editing RTE or related elements\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) &&\n      // Click outside the current editor container\n      !isInsidePopup &&\n      // Click outside the popup\n      !isInsideJoditPopup &&\n      // Click outside the WYSIWYG editor\n      !isInsideWorkplacePopup &&\n      // Click outside the workplace popup\n      !isSelectionMarker &&\n      // Click outside selection markers\n      !isLinkPopup &&\n      // Click outside link input popup\n      !isInsideToolbarButton &&\n      // Click outside the toolbar button\n      !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null); // Close the currently editing RTE\n        setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          //(editorRef.current as any).editor.focus();\n        }, 50);\n      }\n    }\n  }, [editingRTEId]);\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n\n    // Check if this is an AI-created guide\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    console.log(\"RTEsection handleUpdate:\", {\n      createWithAI,\n      selectedTemplate,\n      selectedTemplateTour,\n      isAIAnnouncement,\n      isAITour,\n      isTourBanner,\n      containerId,\n      newContent: newContent.substring(0, 50) + \"...\"\n    });\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        // For Tour+Announcement, use toolTipGuideMetaData\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          // Use the tooltip-specific handler for tour announcements\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        // For pure Announcements, use announcementGuideMetaData\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          // Use the announcement-specific handler\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        // Use the tooltip-specific handler for all tour step types\n        handleTooltipRTEValue(containerId, newContent);\n        console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\n      } else {\n        var _toolTipGuideMetaData5, _toolTipGuideMetaData6;\n        console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\n          currentStepIndex,\n          containerId,\n          availableContainers: (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData5 === void 0 ? void 0 : (_toolTipGuideMetaData6 = _toolTipGuideMetaData5.containers) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.map(c => ({\n            id: c.id,\n            type: c.type\n          }))\n        });\n      }\n    } else {\n      // For non-AI content or other cases, use the regular RTE container system\n      updateRTEContainer(containerId, rteId, newContent);\n      console.log(\"Used updateRTEContainer for non-AI content\");\n    }\n    setIsUnSavedChanges(true);\n  };\n  const handleCloneContainer = containerId => {\n    // Check if cloning is disabled due to section limits\n    if (isCloneDisabled) {\n      return; // Don't clone if limit is reached\n    }\n\n    // Call the clone function from the store\n    cloneRTEContainer(containerId);\n\n    // Call the onClone callback if provided\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    // Check if this is an AI-created announcement\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      // For AI announcements, we need to remove from announcementGuideMetaData\n      // This would require a new function in the store, for now just call the existing one\n      clearRteDetails(containerId, rteId);\n    } else {\n      // For banners and non-AI content, use the regular clear function\n      clearRteDetails(containerId, rteId);\n    }\n\n    // Call the handleDeleteRTESection callback to update section counts\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n      // Don't set editing state, just show toolbar\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n  const config = useMemo(() => ({\n    readonly: false,\n    // all options from https://xdsoft.net/jodit/docs/,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    // Jodit uses 'direction' not just 'rtl'\n    language: 'en',\n    // Optional: change language as well\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    pastePlain: true,\n    // forces plain text pasting\n    askBeforePasteHTML: false,\n    // disables the paste popup dialog\n    askBeforePasteFromWord: false,\n    // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\n    toolbar: toolbarVisibleRTEId !== null,\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    autofocus: true,\n    // Fix dialog positioning by setting popup root to document body\n    popupRoot: document.body,\n    // Fix popup positioning issues\n    zIndex: 100000,\n    globalFullSize: false,\n    // Fix link dialog positioning\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    // Dialog configuration\n    dialog: {\n      zIndex: 100001\n    },\n    cursorAfterAutofocus: 'end',\n    events: {\n      onPaste: handlePaste // Attach custom onPaste handler\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection, toolbarVisibleRTEId]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    // For pure AI announcements (not in tours), use announcementGuideMetaData\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData7;\n    // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\n    if ((_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n      console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\n        totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\n        rteContainers: containersToRender.length,\n        rteData: containersToRender.map(c => ({\n          id: c.id,\n          rteBoxValue: c.rteBoxValue\n        }))\n      });\n    } else {\n      console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\n      containersToRender = [];\n    }\n  } else {\n    // For non-AI content, use rtesContainer\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\n        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        // For non-AI content, get data from rtesContainer\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-status-bar\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-add-new-line\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"150px !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          // Fix Jodit dialog positioning - target correct classes\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          // Fix for link dialog specifically\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\" ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  height: \"24px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true),\n          placement: \"top\",\n          slotProps: {\n            tooltip: {\n              sx: {\n                backgroundColor: 'white',\n                color: 'black',\n                borderRadius: '4px',\n                padding: '0px 4px',\n                border: \"1px dashed var(--primarycolor)\",\n                marginBottom: '30px !important'\n              }\n            }\n          },\n          PopperProps: {\n            modifiers: [{\n              name: 'preventOverflow',\n              options: {\n                boundary: 'viewport'\n              }\n            }, {\n              name: 'flip',\n              options: {\n                enabled: true\n              }\n            }]\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\",\n              position: \"relative\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n              ref: currentEditorRef,\n              value: rteText,\n              config: config,\n              onChange: newContent => handleUpdate(newContent, rteId, id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                toggleToolbar(id);\n              },\n              sx: {\n                position: \"absolute\",\n                bottom: \"2px\",\n                right: \"2px\",\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                zIndex: 1000,\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              title: translate(\"Toggle Toolbar\"),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: e => {\n              e.stopPropagation();\n              toggleToolbar(id);\n            },\n            sx: {\n              position: \"absolute\",\n              bottom: \"2px\",\n              right: \"2px\",\n              width: \"24px\",\n              height: \"24px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              zIndex: 1000,\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              \"& svg\": {\n                width: \"16px\",\n                height: \"16px\"\n              }\n            },\n            title: translate(\"Toggle Toolbar\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: editicon\n              },\n              style: {\n                height: '16px',\n                width: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 33\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"tLECwGDjJccaTXQc0rWOzlmuTLc=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"tLECwGDjJccaTXQc0rWOzlmuTLc=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "contentRef", "editor<PERSON><PERSON><PERSON>", "Map", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "handleUpdate", "newContent", "containerId", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "console", "log", "substring", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "warn", "availableContainers", "map", "c", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "content", "editor", "selection", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "paste<PERSON>lain", "askBeforePasteHTML", "askBeforePasteFromWord", "toolbar", "buttons", "name", "iconURL", "list", "autofocus", "popupRoot", "zIndex", "globalFullSize", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "cursorAfterAutofocus", "events", "onPaste", "controls", "font", "containersToRender", "_toolTipGuideMetaData7", "filter", "totalContainers", "length", "rteContainers", "rteData", "rteBoxValue", "children", "item", "rteText", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "boxShadow", "className", "title", "size", "onClick", "disabled", "backgroundColor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "style", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "tooltip", "color", "padding", "marginBottom", "PopperProps", "modifiers", "options", "boundary", "enabled", "value", "onChange", "e", "stopPropagation", "bottom", "right", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef,useMemo } from \"react\";\r\nimport { Box, TextField, Tooltip, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n\r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor - now works with individual RTEs\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return; // No RTE is currently being edited\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                // Get the container ref for the currently editing RTE\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                // Check if the target is inside the currently editing RTE or related elements\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) && // Click outside the current editor container\r\n                    !isInsidePopup && // Click outside the popup\r\n                    !isInsideJoditPopup && // Click outside the WYSIWYG editor\r\n                    !isInsideWorkplacePopup && // Click outside the workplace popup\r\n                    !isSelectionMarker && // Click outside selection markers\r\n                    !isLinkPopup && // Click outside link input popup\r\n                    !isInsideToolbarButton && // Click outside the toolbar button\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null); // Close the currently editing RTE\r\n                    setToolbarVisibleRTEId(null); // Also hide toolbar when clicking outside\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        //(editorRef.current as any).editor.focus();\r\n                    }, 50);\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            // Check if this is an AI-created guide\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            console.log(\"RTEsection handleUpdate:\", {\r\n                createWithAI,\r\n                selectedTemplate,\r\n                selectedTemplateTour,\r\n                isAIAnnouncement,\r\n                isAITour,\r\n                isTourBanner,\r\n                containerId,\r\n                newContent: newContent.substring(0, 50) + \"...\"\r\n            });\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    // For Tour+Announcement, use toolTipGuideMetaData\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        // Use the tooltip-specific handler for tour announcements\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    // For pure Announcements, use announcementGuideMetaData\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        // Use the announcement-specific handler\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                // For AI Tour with Banner, Tooltip, or Hotspot steps, use toolTipGuideMetaData\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    // Use the tooltip-specific handler for all tour step types\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                    console.log(`Used handleTooltipRTEValue for ${selectedTemplateTour} step in AI tour`);\r\n                } else {\r\n                    console.warn(`No tooltip container found for ${selectedTemplateTour} step`, {\r\n                        currentStepIndex,\r\n                        containerId,\r\n                        availableContainers: toolTipGuideMetaData[currentStepIndex]?.containers?.map(c => ({ id: c.id, type: c.type }))\r\n                    });\r\n                }\r\n            } else {\r\n                // For non-AI content or other cases, use the regular RTE container system\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n                console.log(\"Used updateRTEContainer for non-AI content\");\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n        };\r\n        const handleCloneContainer = (containerId: string) => {\r\n            // Check if cloning is disabled due to section limits\r\n            if (isCloneDisabled) {\r\n                return; // Don't clone if limit is reached\r\n            }\r\n\r\n            // Call the clone function from the store\r\n            cloneRTEContainer(containerId);\r\n\r\n            // Call the onClone callback if provided\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n        const handleDeleteSection = (containerId: string, rteId:string) => {\r\n            // Check if this is an AI-created announcement\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                // For AI announcements, we need to remove from announcementGuideMetaData\r\n                // This would require a new function in the store, for now just call the existing one\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                // For banners and non-AI content, use the regular clear function\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            // Call the handleDeleteRTESection callback to update section counts\r\n            handleDeleteRTESection(index);\r\n        };\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n        const toggleToolbar = (rteId: string) => {\r\n            if (toolbarVisibleRTEId === rteId) {\r\n                setToolbarVisibleRTEId(null);\r\n            } else {\r\n                setToolbarVisibleRTEId(rteId);\r\n                // Don't set editing state, just show toolbar\r\n            }\r\n        };\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        useEffect(() => {\r\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n}, []);\r\n    const config = useMemo(\r\n        (): any => ({\r\n            readonly: false, // all options from https://xdsoft.net/jodit/docs/,\r\n            direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n\r\n// Jodit uses 'direction' not just 'rtl'\r\n        language:  'en', // Optional: change language as well\r\n            toolbarSticky: false,\r\n            toolbarAdaptive: false,\r\n            pastePlain: true,             // forces plain text pasting\r\n  askBeforePasteHTML: false,    // disables the paste popup dialog\r\n  askBeforePasteFromWord: false,\r\n            // Hide toolbar by default, will be controlled by toolbarVisibleRTEId state\r\n            toolbar: toolbarVisibleRTEId !== null,\r\n            buttons: [\r\n\r\n        'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n        'font', 'fontsize', 'link',\r\n        {\r\n            name: 'more',\r\n            iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n            list: [\r\n                        'source',\r\n                        'image', 'video', 'table',\r\n                'align', 'undo', 'redo', '|',\r\n                'hr', 'eraser', 'copyformat',\r\n                'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                'outdent', 'indent', 'paragraph',\r\n            ]\r\n        }\r\n    ],\r\n    autofocus: true,\r\n    // Fix dialog positioning by setting popup root to document body\r\n    popupRoot: document.body,\r\n    // Fix popup positioning issues\r\n    zIndex: 100000,\r\n    globalFullSize: false,\r\n    // Fix link dialog positioning\r\n    link: {\r\n        followOnDblClick: false,\r\n        processVideoLink: true,\r\n        processPastedLink: true,\r\n        openInNewTabCheckbox: true,\r\n        noFollowCheckbox: false,\r\n        modeClassName: 'input' as const,\r\n    },\r\n    // Dialog configuration\r\n    dialog: {\r\n        zIndex: 100001,\r\n    },\r\n    cursorAfterAutofocus: 'end' as const,\r\n    events: {\r\n                onPaste: handlePaste, // Attach custom onPaste handler\r\n    },\r\n    controls: {\r\n        font: {\r\n            list: {\r\n                \"Poppins, sans-serif\": \"Poppins\",\r\n                \"Roboto, sans-serif\": \"Roboto\",\r\n                \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                \"Open Sans, sans-serif\": \"Open Sans\",\r\n                \"Calibri, sans-serif\": \"Calibri\",\r\n                \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n            }\r\n        }\r\n            }\r\n    }),[isRtlDirection, toolbarVisibleRTEId]\r\n\r\n    );\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            // For pure AI announcements (not in tours), use announcementGuideMetaData\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            // For AI Tour with any step type (Banner, Tooltip, Hotspot, or Announcement), use toolTipGuideMetaData\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n                console.log(`RTEsection: Using toolTipGuideMetaData containers for ${selectedTemplateTour} step ${currentStepIndex}:`, {\r\n                    totalContainers: toolTipGuideMetaData[currentStepIndex].containers.length,\r\n                    rteContainers: containersToRender.length,\r\n                    rteData: containersToRender.map(c => ({ id: c.id, rteBoxValue: c.rteBoxValue }))\r\n                });\r\n            } else {\r\n                console.warn(`RTEsection: No toolTipGuideMetaData found for ${selectedTemplateTour} step ${currentStepIndex}`);\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            // For non-AI content, use rtesContainer\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        // For AI announcements and AI tour steps (banner, tooltip, hotspot, announcement), get data from container\r\n                        // Both announcementGuideMetaData and toolTipGuideMetaData use the same structure for RTE containers\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        // For non-AI content, get data from rtesContainer\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-status-bar\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-add-new-line\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    maxHeight: (\r\n  selectedTemplate === \"Banner\" ||\r\n  (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n)\r\n  ? \"50px !important\"\r\n  : (\r\n      selectedTemplate === \"Announcement\" ||\r\n      (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n    )\r\n    ? \"150px !important\"\r\n    : null,\r\n                                    overflow: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ?\"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth:selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null,\r\n                                    minHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\": null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate===\"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null,\r\n                                    maxHeight: selectedTemplate===\"Banner\"|| (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\": null\r\n                                },\r\n                                // Fix Jodit dialog positioning - target correct classes\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                // Fix for link dialog specifically\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {/* Always show Jodit editor, but conditionally show tooltips for non-Banner templates */}\r\n                            {(selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? (\r\n\r\n                                <Tooltip\r\n                                    title={\r\n                                        <>\r\n                                            <IconButton\r\n                                                size=\"small\"\r\n                                                onClick={() => handleCloneContainer(item.id)}\r\n                                                disabled={isCloneDisabled}\r\n                                                title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        height: \"24px\",\r\n                                                        path: {\r\n                                                            fill:\"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                    }}\r\n                                                >\r\n                                                <span\r\n                                                    dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                    style={{\r\n                                                        opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                            <IconButton size=\"small\" onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                            sx={{\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        path: {\r\n                                                            fill:\"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                }}\r\n                                            >\r\n                                                <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                    style={{\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                        </>\r\n                                    }\r\n                                    placement=\"top\"\r\n                                    slotProps={{\r\n                                        tooltip: {\r\n                                            sx: {\r\n                                                backgroundColor: 'white',\r\n                                                color: 'black',\r\n                                                borderRadius: '4px',\r\n                                                padding: '0px 4px',\r\n                                                border: \"1px dashed var(--primarycolor)\",\r\n                                                marginBottom: '30px !important'\r\n                                            },\r\n                                        },\r\n                                    }}\r\n                                    PopperProps={{\r\n                                        modifiers: [\r\n                                            {\r\n                                                name: 'preventOverflow',\r\n                                                options: { boundary: 'viewport' },\r\n                                            },\r\n                                            {\r\n                                                name: 'flip',\r\n                                                options: { enabled: true },\r\n                                            },\r\n                                        ],\r\n                                    }}\r\n                                >\r\n                                    <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                        <JoditEditor\r\n                                            ref={currentEditorRef}\r\n                                            value={rteText}\r\n                                            config={config}\r\n                                            onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                        />\r\n                                        {/* Edit icon for toolbar toggle - positioned at bottom right */}\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                toggleToolbar(id);\r\n                                            }}\r\n                                            sx={{\r\n                                                position: \"absolute\",\r\n                                                bottom: \"2px\",\r\n                                                right: \"2px\",\r\n                                                width: \"24px\",\r\n                                                height: \"24px\",\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                zIndex: 1000,\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                \"& svg\": {\r\n                                                    width: \"16px\",\r\n                                                    height: \"16px\",\r\n                                                }\r\n                                            }}\r\n                                            title={translate(\"Toggle Toolbar\")}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                                style={{ height: '16px', width: '16px' }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n                                </Tooltip>\r\n                            ) : (\r\n                                <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                    <JoditEditor\r\n                                        ref={currentEditorRef}\r\n                                        value={rteText}\r\n                                        config={config}\r\n                                        onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                    />\r\n                                    {/* Edit icon for toolbar toggle - positioned at bottom right */}\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            toggleToolbar(id);\r\n                                        }}\r\n                                        sx={{\r\n                                            position: \"absolute\",\r\n                                            bottom: \"2px\",\r\n                                            right: \"2px\",\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            zIndex: 1000,\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                        title={translate(\"Toggle Toolbar\")}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                            style={{ height: '16px', width: '16px' }}\r\n                                        />\r\n                                    </IconButton>\r\n                                </div>\r\n                            )}\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAACC,OAAO,QAAQ,OAAO;AAC9E,SAASC,GAAG,EAAaC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnE,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,6BAA6B;AAE5E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGhB,UAAU,CAAAiB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAMiD,UAAU,GAAG/C,MAAM,CAAS,EAAE,CAAC;;EAErC;EACA,MAAMgD,UAAU,GAAGhD,MAAM,CAAoC,IAAIiD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAMC,aAAa,GAAGlD,MAAM,CAA+C,IAAIiD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAME,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACJ,UAAU,CAACK,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCJ,UAAU,CAACK,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEvD,KAAK,CAAC2D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOR,UAAU,CAACK,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAEvD,KAAK,CAAC2D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACZ,MAAM4D,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACrB,YAAY,EAAE,OAAO,CAAC;;MAE3B,MAAMsB,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;;MAErG;MACA,MAAMc,mBAAmB,GAAGvB,eAAe,CAACf,YAAY,CAAC;;MAEzD;MACA,IACIsC,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE5B,OAAO,IAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAAI;MAC/D,CAACG,aAAa;MAAI;MAClB,CAACI,kBAAkB;MAAI;MACvB,CAACC,sBAAsB;MAAI;MAC3B,CAACC,iBAAiB;MAAI;MACtB,CAACG,WAAW;MAAI;MAChB,CAACC,qBAAqB;MAAI;MAC1B,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACExB,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;QACvBE,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC;IAEDwB,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAElB5C,SAAS,CAAC,MAAM;IACZ,IAAI4C,YAAY,EAAE;MACd,MAAMyC,SAAS,GAAGjC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAIyC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpBgC,UAAU,CAAC,MAAM;UACb;QAAA,CACH,EAAE,EAAE,CAAC;MACV;IACJ;EACJ,CAAC,EAAE,CAAC1C,YAAY,CAAC,CAAC;EAIlB,MAAM2C,YAAY,GAAGA,CAACC,UAAkB,EAAEnC,KAAa,EAAEoC,WAAmB,KAAK;IAC7EzC,UAAU,CAACM,OAAO,GAAGkC,UAAU;;IAE/B;IACA,MAAME,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAMuD,QAAQ,GAAGlD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAMyD,kBAAkB,GAAGD,QAAQ,IAAIvD,oBAAoB,KAAK,cAAc;IAC9E,MAAMyD,YAAY,GAAGF,QAAQ,IAAIvD,oBAAoB,KAAK,QAAQ;IAClE,MAAM0D,aAAa,GAAGH,QAAQ,KAAKvD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5G2D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACpCvD,YAAY;MACZN,gBAAgB;MAChBC,oBAAoB;MACpBsD,gBAAgB;MAChBC,QAAQ;MACRE,YAAY;MACZJ,WAAW;MACXD,UAAU,EAAEA,UAAU,CAACS,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG;IAC9C,CAAC,CAAC;IAEF,IAAIP,gBAAgB,EAAE;MAClB,MAAMQ,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;MAExC,IAAIkD,kBAAkB,EAAE;QAAA,IAAAO,qBAAA,EAAAC,sBAAA;QACpB;QACA,MAAMC,gBAAgB,IAAAF,qBAAA,GAAG7D,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClB;UACA7D,qBAAqB,CAACiD,WAAW,EAAED,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAkB,qBAAA,EAAAC,sBAAA;QACH;QACA,MAAMC,qBAAqB,IAAAF,qBAAA,GAAGrE,yBAAyB,CAAC6D,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB;UACArE,0BAA0B,CAACkD,WAAW,EAAED,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIG,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAe,sBAAA,EAAAC,sBAAA;MACpD;MACA,MAAMZ,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;MACxC,MAAM2D,gBAAgB,IAAAQ,sBAAA,GAAGvE,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAAC3B,EAAE,KAAKY,WAAW,IAAIe,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClB;QACA7D,qBAAqB,CAACiD,WAAW,EAAED,UAAU,CAAC;QAC9CO,OAAO,CAACC,GAAG,CAAC,kCAAkC5D,oBAAoB,kBAAkB,CAAC;MACzF,CAAC,MAAM;QAAA,IAAA2E,sBAAA,EAAAC,sBAAA;QACHjB,OAAO,CAACkB,IAAI,CAAC,kCAAkC7E,oBAAoB,OAAO,EAAE;UACxE8D,gBAAgB;UAChBT,WAAW;UACXyB,mBAAmB,GAAAH,sBAAA,GAAEzE,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAa,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCT,UAAU,cAAAU,sBAAA,uBAAlDA,sBAAA,CAAoDG,GAAG,CAACC,CAAC,KAAK;YAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;YAAE4B,IAAI,EAAEW,CAAC,CAACX;UAAK,CAAC,CAAC;QAClH,CAAC,CAAC;MACN;IACJ,CAAC,MAAM;MACH;MACA1E,kBAAkB,CAAC0D,WAAW,EAAEpC,KAAK,EAAEmC,UAAU,CAAC;MAClDO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC7D;IAEAhE,mBAAmB,CAAC,IAAI,CAAC;EAC7B,CAAC;EACD,MAAMqF,oBAAoB,GAAI5B,WAAmB,IAAK;IAClD;IACA,IAAI/D,eAAe,EAAE;MACjB,OAAO,CAAC;IACZ;;IAEA;IACAO,iBAAiB,CAACwD,WAAW,CAAC;;IAE9B;IACA,IAAIhE,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EACD,MAAM6F,mBAAmB,GAAGA,CAAC7B,WAAmB,EAAEpC,KAAY,KAAK;IAC/D;IACA,MAAMqC,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAIsD,gBAAgB,EAAE;MAClB;MACA;MACAxD,eAAe,CAACuD,WAAW,EAAEpC,KAAK,CAAC;IACvC,CAAC,MAAM;MACH;MACAnB,eAAe,CAACuD,WAAW,EAAEpC,KAAK,CAAC;IACvC;;IAEA;IACA/B,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EACD,MAAMgG,WAAW,GAAI1D,KAA2C,IAAK;IACjEA,KAAK,CAAC2D,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG5D,KAAK,CAAC4D,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAGD,MAAMK,aAAa,GAAIC,OAAe,IAAK;IACvC,IAAIpF,YAAY,EAAE;MACd,MAAMyC,SAAS,GAAGjC,YAAY,CAACR,YAAY,CAAC;MAC5C,IAAIyC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpB,MAAM2E,MAAM,GAAI5C,SAAS,CAAC/B,OAAO,CAAS2E,MAAM;QAChDA,MAAM,CAACC,SAAS,CAACC,UAAU,CAACH,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAMI,aAAa,GAAI/E,KAAa,IAAK;IACrC,IAAIP,mBAAmB,KAAKO,KAAK,EAAE;MAC/BN,sBAAsB,CAAC,IAAI,CAAC;IAChC,CAAC,MAAM;MACHA,sBAAsB,CAACM,KAAK,CAAC;MAC7B;IACJ;EACJ,CAAC;EACD,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGvI,QAAQ,CAAU,KAAK,CAAC;EACpEC,SAAS,CAAC,MAAM;IACpB,MAAMuI,GAAG,GAAGhE,QAAQ,CAACiE,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACF,MAAMC,MAAM,GAAGxI,OAAO,CAClB,OAAY;IACRyI,QAAQ,EAAE,KAAK;IAAE;IACjBC,SAAS,EAAER,cAAc,GAAG,KAAK,GAAY,KAAc;IAEvE;IACQS,QAAQ,EAAG,IAAI;IAAE;IACbC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,UAAU,EAAE,IAAI;IAAc;IACxCC,kBAAkB,EAAE,KAAK;IAAK;IAC9BC,sBAAsB,EAAE,KAAK;IACnB;IACAC,OAAO,EAAEtG,mBAAmB,KAAK,IAAI;IACrCuG,OAAO,EAAE,CAEb,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACM,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACjC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IACDC,SAAS,EAAE,IAAI;IACf;IACAC,SAAS,EAAEnF,QAAQ,CAACiE,IAAI;IACxB;IACAmB,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IACrB;IACAC,IAAI,EAAE;MACFC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACnB,CAAC;IACD;IACAC,MAAM,EAAE;MACJT,MAAM,EAAE;IACZ,CAAC;IACDU,oBAAoB,EAAE,KAAc;IACpCC,MAAM,EAAE;MACIC,OAAO,EAAEhD,WAAW,CAAE;IAClC,CAAC;IACDiD,QAAQ,EAAE;MACNC,IAAI,EAAE;QACFjB,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACI;EACR,CAAC,CAAC,EAAC,CAACnB,cAAc,EAAEvF,mBAAmB,CAEvC,CAAC;;EAEG;EACA,MAAM4C,gBAAgB,GAAGjD,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMuD,QAAQ,GAAGlD,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAMyD,kBAAkB,GAAGD,QAAQ,IAAIvD,oBAAoB,KAAK,cAAc;EAC9E,MAAMyD,YAAY,GAAGF,QAAQ,IAAIvD,oBAAoB,KAAK,QAAQ;EAClE,MAAM0D,aAAa,GAAGH,QAAQ,KAAKvD,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAM8D,gBAAgB,GAAGxD,WAAW,GAAG,CAAC;EAExC,IAAIgI,kBAAyB,GAAG,EAAE;EAElC,IAAIhF,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzC;IACA8E,kBAAkB,GAAG/H,8BAA8B,CAACuD,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIP,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAA+E,sBAAA;IAC1E;IACA,KAAAA,sBAAA,GAAIrI,oBAAoB,CAAC4D,gBAAgB,CAAC,cAAAyE,sBAAA,eAAtCA,sBAAA,CAAwCrE,UAAU,EAAE;MACpDoE,kBAAkB,GAAGpI,oBAAoB,CAAC4D,gBAAgB,CAAC,CAACI,UAAU,CAACsE,MAAM,CAACxD,CAAC,IAAIA,CAAC,CAACX,IAAI,KAAK,KAAK,CAAC;MACpGV,OAAO,CAACC,GAAG,CAAC,yDAAyD5D,oBAAoB,SAAS8D,gBAAgB,GAAG,EAAE;QACnH2E,eAAe,EAAEvI,oBAAoB,CAAC4D,gBAAgB,CAAC,CAACI,UAAU,CAACwE,MAAM;QACzEC,aAAa,EAAEL,kBAAkB,CAACI,MAAM;QACxCE,OAAO,EAAEN,kBAAkB,CAACvD,GAAG,CAACC,CAAC,KAAK;UAAEvC,EAAE,EAAEuC,CAAC,CAACvC,EAAE;UAAEoG,WAAW,EAAE7D,CAAC,CAAC6D;QAAY,CAAC,CAAC;MACnF,CAAC,CAAC;IACN,CAAC,MAAM;MACHlF,OAAO,CAACkB,IAAI,CAAC,iDAAiD7E,oBAAoB,SAAS8D,gBAAgB,EAAE,CAAC;MAC9GwE,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACH;IACAA,kBAAkB,GAAG5I,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAAkK,QAAA,EACKR,kBAAkB,CAACvD,GAAG,CAAEgE,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAI/H,KAAK,GAAG,EAAE;MACd,IAAIwB,EAAE,GAAG,EAAE;MAEX,IAAKa,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH;QACA;QACAwF,OAAO,GAAGD,IAAI,CAACF,WAAW,IAAI,EAAE;QAChC5H,KAAK,GAAG8H,IAAI,CAACtG,EAAE;QACfA,EAAE,GAAGsG,IAAI,CAACtG,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAwG,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACH;QACAJ,OAAO,GAAG,EAAAC,UAAA,GAAAF,IAAI,CAACM,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCrI,KAAK,IAAAkI,WAAA,GAAGJ,IAAI,CAACM,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB3G,EAAE;QAC1BA,EAAE,GAAGsG,IAAI,CAACtG,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAGpB,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAM8G,gBAAgB,GAAGvI,YAAY,CAACyB,EAAE,CAAC;MAEzC,oBACI/D,OAAA,CAACV,GAAG;QAEAuB,GAAG,EAAEuD,mBAAoB;QACzB0G,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,0BAA0B,EAAE;YACxBF,OAAO,EAAE;UACb,CAAC;UACD,qBAAqB,EAAE;YACnBA,OAAO,EAAE;UACb,CAAC;UACD,uBAAuB,EAAE;YACrBA,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAE/J,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDgK,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBC,SAAS,EAAElK,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACtIkK,SAAS,EAC3CnK,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAExE,kBAAkB,GAClB,IAAI;YAC0BmK,QAAQ,EAAEpK,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAE,QAAQ,GAAG;UAC5H,CAAC;UACD,kBAAkB,EAAE;YAChB6J,QAAQ,EAAC9J,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACpIiK,SAAS,EAAElK,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD,qBAAqB,EAAE;YACnByJ,OAAO,EAAE,iBAAiB;YAC1BW,cAAc,EAAE,mBAAmB;YACnCC,MAAM,EAAEtK,gBAAgB,KAAG,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE,IAAI;YACnIkK,SAAS,EAAEnK,gBAAgB,KAAG,QAAQ,IAAIA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAE;UACrI,CAAC;UACD;UACA,qBAAqB,EAAE;YACnB2J,QAAQ,EAAE,kBAAkB;YAC5BpC,MAAM,EAAE,mBAAmB;YAC3B+C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCb,QAAQ,EAAE,qBAAqB;YAC/BW,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD;UACA,qBAAqB,EAAE;YACnBlB,QAAQ,EAAE,kBAAkB;YAC5BpC,MAAM,EAAE,mBAAmB;YAC3B+C,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf;QACJ,CAAE;QACFM,SAAS,EAAC,WAAW;QAAAhC,QAAA,EAGnB/I,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,gBAE/NtB,OAAA,CAACT,OAAO;UACJ8M,KAAK,eACDrM,OAAA,CAAAE,SAAA;YAAAkK,QAAA,gBACIpK,OAAA,CAACR,UAAU;cACP8M,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMhG,oBAAoB,CAAC8D,IAAI,CAACtG,EAAE,CAAE;cAC7CyI,QAAQ,EAAE5L,eAAgB;cAC1ByL,KAAK,EAAEzL,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjH+J,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACrB,CAAC;gBACDC,GAAG,EAAE;kBACDf,MAAM,EAAE,MAAM;kBACdgB,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACA,CAAE;cAAAxC,QAAA,eAENpK,OAAA;gBACI6M,uBAAuB,EAAE;kBAAEC,MAAM,EAAEnN;gBAAS,CAAE;gBAC9CoN,KAAK,EAAE;kBACHC,OAAO,EAAEpM,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClC+K,MAAM,EAAE;gBACZ;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACbpN,OAAA,CAACR,UAAU;cAAC8M,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAM/F,mBAAmB,CAAC6D,IAAI,CAACtG,EAAE,EAAExB,KAAK,CAAE;cAC5EuI,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACjB,CAAC;gBACDC,GAAG,EAAE;kBACDC,IAAI,EAAE;oBACFC,IAAI,EAAC;kBACT;gBACJ;cACJ,CAAE;cAAAxC,QAAA,eAEFpK,OAAA;gBAAM6M,uBAAuB,EAAE;kBAAEC,MAAM,EAAElN;gBAAW,CAAE;gBAClDmN,KAAK,EAAE;kBACHpB,MAAM,EAAE;gBACZ;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,eACf,CACL;UACDC,SAAS,EAAC,KAAK;UACfC,SAAS,EAAE;YACPC,OAAO,EAAE;cACLzC,EAAE,EAAE;gBACA2B,eAAe,EAAE,OAAO;gBACxBe,KAAK,EAAE,OAAO;gBACdtB,YAAY,EAAE,KAAK;gBACnBuB,OAAO,EAAE,SAAS;gBAClBxB,MAAM,EAAE,gCAAgC;gBACxCyB,YAAY,EAAE;cAClB;YACJ;UACJ,CAAE;UACFC,WAAW,EAAE;YACTC,SAAS,EAAE,CACP;cACIpF,IAAI,EAAE,iBAAiB;cACvBqF,OAAO,EAAE;gBAAEC,QAAQ,EAAE;cAAW;YACpC,CAAC,EACD;cACItF,IAAI,EAAE,MAAM;cACZqF,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAK;YAC7B,CAAC;UAET,CAAE;UAAA3D,QAAA,eAEFpK,OAAA;YAAK+M,KAAK,EAAE;cAAE3B,KAAK,EAAE,MAAM;cAAEH,QAAQ,EAAE;YAAW,CAAE;YAAAb,QAAA,gBAChDpK,OAAA,CAACP,WAAW;cACRoB,GAAG,EAAEgK,gBAAiB;cACtBmD,KAAK,EAAE1D,OAAQ;cACfzC,MAAM,EAAEA,MAAO;cACfoG,QAAQ,EAAGvJ,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEnC,KAAK,EAAEwB,EAAE;YAAE;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eAEFpN,OAAA,CAACR,UAAU;cACP8M,IAAI,EAAC,OAAO;cACZC,OAAO,EAAG2B,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB7G,aAAa,CAACvD,EAAE,CAAC;cACrB,CAAE;cACF+G,EAAE,EAAE;gBACAG,QAAQ,EAAE,UAAU;gBACpBmD,MAAM,EAAE,KAAK;gBACbC,KAAK,EAAE,KAAK;gBACZjD,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACdc,eAAe,EAAE,0BAA0B;gBAC3C5D,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE;kBACP4D,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLrB,KAAK,EAAE,MAAM;kBACbO,MAAM,EAAE;gBACZ;cACJ,CAAE;cACFU,KAAK,EAAEtL,SAAS,CAAC,gBAAgB,CAAE;cAAAqJ,QAAA,eAEnCpK,OAAA;gBACI6M,uBAAuB,EAAE;kBAAEC,MAAM,EAAEjN;gBAAS,CAAE;gBAC9CkN,KAAK,EAAE;kBAAEpB,MAAM,EAAE,MAAM;kBAAEP,KAAK,EAAE;gBAAO;cAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAEVpN,OAAA;UAAK+M,KAAK,EAAE;YAAE3B,KAAK,EAAE,MAAM;YAAEH,QAAQ,EAAE;UAAW,CAAE;UAAAb,QAAA,gBAChDpK,OAAA,CAACP,WAAW;YACRoB,GAAG,EAAEgK,gBAAiB;YACtBmD,KAAK,EAAE1D,OAAQ;YACfzC,MAAM,EAAEA,MAAO;YACfoG,QAAQ,EAAGvJ,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAEnC,KAAK,EAAEwB,EAAE;UAAE;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAEFpN,OAAA,CAACR,UAAU;YACP8M,IAAI,EAAC,OAAO;YACZC,OAAO,EAAG2B,CAAC,IAAK;cACZA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnB7G,aAAa,CAACvD,EAAE,CAAC;YACrB,CAAE;YACF+G,EAAE,EAAE;cACAG,QAAQ,EAAE,UAAU;cACpBmD,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE,KAAK;cACZjD,KAAK,EAAE,MAAM;cACbO,MAAM,EAAE,MAAM;cACdc,eAAe,EAAE,0BAA0B;cAC3C5D,MAAM,EAAE,IAAI;cACZ,SAAS,EAAE;gBACP4D,eAAe,EAAE;cACrB,CAAC;cACD,OAAO,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE;cACZ;YACJ,CAAE;YACFU,KAAK,EAAEtL,SAAS,CAAC,gBAAgB,CAAE;YAAAqJ,QAAA,eAEnCpK,OAAA;cACI6M,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjN;cAAS,CAAE;cAC9CkN,KAAK,EAAE;gBAAEpB,MAAM,EAAE,MAAM;gBAAEP,KAAK,EAAE;cAAO;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACR,GA/OIrJ,EAAE;QAAAkJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgPN,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAvmB4BtN,cAAc,EAgBnCJ,cAAc;AAAA,EAwlB1B,CAAC;EAAA,QAxmBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAwlBzB;AAAC4O,GAAA,GA1mBInO,UAAqC;AA4mB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAiO,GAAA;AAAAC,YAAA,CAAAlO,EAAA;AAAAkO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}